<template>
  <div v-loading="loading" style="width: 100%; height: 100%">
    <div
      v-if="proxy"
      v-html="html"
      style="width: 100%; height: 100%; overflow: auto; padding: 10px"></div>
    <iframe
      v-else
      :src="url"
      frameborder="no"
      style="width: 100%; height: 100%"
      scrolling="auto" />
  </div>
</template>

<script setup>
import { getLinkHtml } from "@/api/article.js";
const props = defineProps({
  src: {
    type: String,
    required: true,
  },
  // 如果为true，就显示接口返回的html内容
  proxy: {
    type: Boolean,
    default: false,
  },
});
let html = ref("");
const loading = ref(true);
const url = computed(() => props.src);
watch(
  () => props.src,
  (val) => {
    if (val) {
      loading.value = true;
      getLinkHtml({ link: val })
        .then((res) => {
          html.value = res;
        })
        .finally(() => {
          loading.value = false;
        });
    }
  },
  { immediate: true }
);
onMounted(() => {
  setTimeout(() => {
    loading.value = false;
  }, 300);
});
</script>
