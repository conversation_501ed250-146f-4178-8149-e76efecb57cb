import requests
from bs4 import BeautifulSoup
import re
import time
import chardet
from urllib.parse import urlparse
from typing import Dict, List, Optional, Union


class UniversalHTMLExtractor:
    """
    通用HTML内容提取器
    
    这是一个基于您多个demo的通用解决方案，可以智能处理各种类型的网站
    包含了编码检测、多重选择器、内容清理、文件保存等功能
    """

    def __init__(self, custom_headers: Optional[Dict] = None):
        """
        初始化提取器
        
        Args:
            custom_headers: 自定义请求头，如果不提供则使用默认值
        """
        # 默认请求头，模拟真实浏览器
        self.default_headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        }
        
        # 如果提供了自定义头部，则合并
        self.headers = custom_headers or self.default_headers
        
        # 预定义的通用选择器配置
        self.selectors_config = self._init_selectors_config()
        
        # 通用的清理选择器
        self.cleanup_selectors = self._init_cleanup_selectors()

    def _init_selectors_config(self) -> Dict:
        """初始化各种内容类型的选择器配置"""
        return {
            # 标题选择器 (按优先级排序)
            'title': [
                'h1[itemprop="name"] a',  # GitHub/StackOverflow
                'h1.article-title',       # 文章类网站
                'h1.question-title',      # 问答类网站
                'h1[data-test="product-name"]',  # ProductHunt
                '.title h1',
                '.entry-title',
                'h1',
                'title'
            ],
            
            # 正文内容选择器
            'content': [
                '.markdown-body',         # GitHub README
                '.article-content',       # 通用文章
                '.entry-content',         # WordPress等
                '.post-content',          # 博客
                '.content',               # 通用内容
                '.s-prose',              # StackOverflow
                '.post-text',            # 论坛
                'article',               # HTML5语义化
                '.article',              # 通用文章类
                '#content_views',        # CSDN等
                '.htmledit_views'        # 编辑器视图
            ],
            
            # 作者信息选择器
            'author': [
                '.author-name',
                '.username',
                '.user-name',
                '.byline .author',
                '[rel="author"]',
                '.author',
                '.creator',
                '.user-info .user-details a',
                '[data-author]'
            ],
            
            # 时间信息选择器
            'time': [
                '.publish-time',
                '.entry-date',
                'time[datetime]',
                '.date',
                '.time',
                '.relativetime',
                '.user-action-time',
                '[datetime]'
            ],
            
            # 描述/摘要选择器
            'description': [
                '[itemprop="about"]',
                '.description',
                '.summary',
                '.abstract',
                '.excerpt',
                '.tagline',
                '[data-test="product-description"]'
            ],
            
            # 标签选择器
            'tags': [
                '.tag',
                '.post-tag',
                '.topic',
                '.category',
                '.label',
                '.badge',
                '[data-test="topic"]'
            ],
            
            # 投票/点赞选择器
            'votes': [
                '.js-vote-count',
                '.vote-count-post',
                '[data-test="vote-count"]',
                '.vote-count',
                '.upvotes',
                '.votes',
                '[data-value]'
            ]
        }

    def _init_cleanup_selectors(self) -> List[str]:
        """初始化需要清理的元素选择器"""
        return [
            # 脚本和样式
            'script', 'style', 'noscript',
            
            # 广告相关
            '.advertisement', '.ad', '.ads', '.adsbygoogle',
            
            # 社交分享
            '.share', '.share-buttons', '.social-share', '.social',
            
            # 导航和菜单
            '.nav', '.navigation', '.navbar', '.menu', '.header', '.footer',
            
            # 侧边栏和小部件
            '.sidebar', '.widget', '.aside',
            
            # 评论相关（可选择性清理）
            '.comment-section', '.comments',
            
            # 相关文章推荐
            '.related', '.recommend', '.recommendation',
            
            # 分页和面包屑
            '.pagination', '.breadcrumb',
            
            # 按钮和操作
            '.btn-group', '.action-buttons', '.follow-button', '.like-button',
            
            # 元信息
            '.meta', '.article-meta', '.post-meta',
            
            # 特定网站的清理
            '.js-file-line-container',  # GitHub
            '.social-count',            # GitHub
            '.js-social-form',          # GitHub
            '.post-menu',              # StackOverflow
            '.post-signature',         # StackOverflow
            '.community-option'        # StackOverflow
        ]

    def extract_content(self, url: str, site_type: str = 'auto') -> Dict:
        """
        通用内容提取方法
        
        Args:
            url (str): 要提取的网页链接
            site_type (str): 网站类型提示 ('auto', 'article', 'github', 'qa', 'product')
            
        Returns:
            dict: 包含提取内容的字典
        """
        try:
            print(f"🚀 开始提取: {url}")
            
            # 发送HTTP请求
            response = self._make_request(url)
            if not response:
                return self._error_result(url, "请求失败")
            
            # 智能检测编码
            encoding = self._detect_encoding(response)
            response.encoding = encoding
            print(f"📝 检测编码: {encoding}")
            
            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 自动检测网站类型（如果未指定）
            if site_type == 'auto':
                site_type = self._detect_site_type(url, soup)
                print(f"🔍 检测网站类型: {site_type}")
            
            # 提取基础信息
            result = {
                'url': url,
                'site_type': site_type,
                'encoding': encoding,
                'title': self._extract_by_selectors(soup, 'title'),
                'content_html': self._extract_main_content(soup, site_type),
                'content_text': '',
                'author': self._extract_by_selectors(soup, 'author'),
                'publish_time': self._extract_by_selectors(soup, 'time'),
                'description': self._extract_by_selectors(soup, 'description'),
                'tags': self._extract_multiple_by_selectors(soup, 'tags'),
                'status': 'success'
            }
            
            # 提取纯文本内容
            result['content_text'] = self._html_to_text(result['content_html'])
            
            # 根据网站类型提取特定信息
            if site_type == 'qa':  # 问答网站
                result.update(self._extract_qa_specific(soup))
            elif site_type == 'github':  # GitHub
                result.update(self._extract_github_specific(soup))
            elif site_type == 'product':  # 产品网站
                result.update(self._extract_product_specific(soup))
            
            print(f"✅ 提取成功: {result['title']}")
            return result
            
        except Exception as e:
            print(f"❌ 提取失败: {str(e)}")
            return self._error_result(url, f"解析失败: {str(e)}")

    def _make_request(self, url: str) -> Optional[requests.Response]:
        """发送HTTP请求"""
        try:
            response = requests.get(url, headers=self.headers, timeout=15)
            response.raise_for_status()
            return response
        except requests.RequestException as e:
            print(f"❌ 请求失败: {str(e)}")
            return None

    def _detect_encoding(self, response: requests.Response) -> str:
        """
        智能检测网页编码
        这是从掘金demo中学到的最佳实践
        """
        # 方法1：从HTTP响应头获取编码
        content_type = response.headers.get('content-type', '').lower()
        if 'charset=' in content_type:
            charset = content_type.split('charset=')[1].split(';')[0].strip()
            if charset:
                return charset

        # 方法2：从HTML meta标签获取编码
        try:
            partial_soup = BeautifulSoup(response.content[:4096], 'html.parser')
            
            # 检查charset属性
            meta_charset = partial_soup.find('meta', attrs={'charset': True})
            if meta_charset:
                return meta_charset['charset']

            # 检查http-equiv类型的meta标签
            meta_http_equiv = partial_soup.find('meta', attrs={'http-equiv': re.compile('Content-Type', re.I)})
            if meta_http_equiv and 'content' in meta_http_equiv.attrs:
                content = meta_http_equiv['content']
                if 'charset=' in content:
                    return content.split('charset=')[1].split(';')[0].strip()
        except Exception:
            pass

        # 方法3：使用chardet库检测编码
        try:
            detected = chardet.detect(response.content)
            if detected['confidence'] > 0.7:
                return detected['encoding']
        except Exception:
            pass

        # 方法4：默认使用UTF-8
        return 'utf-8'

    def _detect_site_type(self, url: str, soup: BeautifulSoup) -> str:
        """
        自动检测网站类型
        """
        domain = urlparse(url).netloc.lower()
        
        # 基于域名判断
        if 'github.com' in domain:
            return 'github'
        elif 'stackoverflow.com' in domain or 'stackexchange.com' in domain:
            return 'qa'
        elif 'producthunt.com' in domain:
            return 'product'
        elif any(blog in domain for blog in ['juejin.cn', 'csdn.net', 'cnblogs.com', 'jianshu.com']):
            return 'article'
        
        # 基于页面内容判断
        if soup.select('.question, .answer'):
            return 'qa'
        elif soup.select('.repository-content, .markdown-body'):
            return 'github'
        elif soup.select('.article-content, .entry-content, .post-content'):
            return 'article'
        else:
            return 'general'

    def _extract_by_selectors(self, soup: BeautifulSoup, selector_type: str) -> str:
        """
        通过选择器列表提取单个内容
        """
        selectors = self.selectors_config.get(selector_type, [])
        
        for selector in selectors:
            element = soup.select_one(selector)
            if element:
                text = element.get_text(strip=True)
                
                # 验证内容的有效性
                if self._is_valid_content(text, selector_type):
                    return text
        
        return f"未找到{selector_type}"

    def _extract_multiple_by_selectors(self, soup: BeautifulSoup, selector_type: str) -> List[str]:
        """
        通过选择器列表提取多个内容（如标签）
        """
        selectors = self.selectors_config.get(selector_type, [])
        results = []
        
        for selector in selectors:
            elements = soup.select(selector)
            for element in elements:
                text = element.get_text(strip=True)
                if text and len(text) < 100 and text not in results:  # 避免重复和过长内容
                    results.append(text)
        
        return results[:20]  # 限制数量

    def _extract_main_content(self, soup: BeautifulSoup, site_type: str) -> str:
        """
        提取主要内容HTML
        """
        # 根据网站类型优化选择器顺序
        content_selectors = self.selectors_config['content'].copy()
        
        if site_type == 'github':
            # GitHub优先查找README
            content_selectors.insert(0, 'article[itemprop="text"]')
            content_selectors.insert(1, '.Box-body.px-5.pb-5')
        elif site_type == 'qa':
            # 问答网站优先查找问题内容
            content_selectors.insert(0, '.question .s-prose')
            content_selectors.insert(1, '.question .post-text')
        
        for selector in content_selectors:
            content_elem = soup.select_one(selector)
            if content_elem:
                # 创建副本避免修改原始DOM
                content_copy = content_elem.__copy__()
                
                # 清理内容
                self._clean_content(content_copy)
                
                # 验证内容长度
                text_content = content_copy.get_text(strip=True)
                if len(text_content) > 100:  # 确保内容有意义
                    return str(content_copy)
        
        return "未找到主要内容"

    def _clean_content(self, content_elem: BeautifulSoup) -> None:
        """
        清理HTML内容，移除不需要的元素
        这汇总了所有demo的清理经验
        """
        # 移除清理列表中的所有元素
        for selector in self.cleanup_selectors:
            for elem in content_elem.select(selector):
                elem.decompose()
        
        # 移除空的标签
        for tag in content_elem.find_all():
            if not tag.get_text(strip=True) and not tag.find_all(['img', 'br', 'hr', 'input']):
                tag.decompose()
        
        # 清理属性（保留基本格式）
        for tag in content_elem.find_all():
            # 保留重要属性
            attrs_to_keep = ['href', 'src', 'alt', 'title']
            new_attrs = {}
            for attr in attrs_to_keep:
                if attr in tag.attrs:
                    new_attrs[attr] = tag.attrs[attr]
            tag.attrs = new_attrs

    def _html_to_text(self, html_content: str) -> str:
        """
        将HTML转换为清洁的纯文本
        """
        if html_content == "未找到主要内容":
            return html_content
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            text = soup.get_text(separator='\n', strip=True)
            
            # 清理文本
            text = re.sub(r'\n\s*\n', '\n\n', text)  # 合并多个空行
            text = re.sub(r'[ \t]+', ' ', text)      # 合并多个空格
            text = re.sub(r'\n +', '\n', text)       # 移除行首空格
            
            return text
        except Exception:
            return "文本提取失败"

    def _is_valid_content(self, text: str, content_type: str) -> bool:
        """
        验证提取内容的有效性
        """
        if not text:
            return False
        
        # 根据内容类型设置不同的验证规则
        min_lengths = {
            'title': 5,
            'author': 2,
            'time': 4,
            'description': 10,
            'content': 50
        }
        
        min_length = min_lengths.get(content_type, 3)
        
        # 长度检查
        if len(text) < min_length:
            return False
        
        # 特殊内容过滤
        invalid_patterns = [
            r'^[\s\n\r]*$',  # 纯空白
            r'^\d+$',        # 纯数字（对于某些类型）
            r'^[^\w]*$'      # 无有意义字符
        ]
        
        for pattern in invalid_patterns:
            if re.match(pattern, text):
                return False
        
        return True

    def _extract_qa_specific(self, soup: BeautifulSoup) -> Dict:
        """提取问答网站特有的信息"""
        return {
            'votes': self._extract_by_selectors(soup, 'votes'),
            'answers_count': len(soup.select('.answer')),
            'has_accepted_answer': bool(soup.select('.accepted-answer'))
        }

    def _extract_github_specific(self, soup: BeautifulSoup) -> Dict:
        """提取GitHub特有的信息"""
        # 提取统计信息
        stats = {}
        
        # 星标数
        star_elem = soup.select_one('#repo-stars-counter-star, .social-count')
        if star_elem:
            stats['stars'] = star_elem.get_text(strip=True)
        
        # Fork数
        fork_elem = soup.select_one('#repo-network-counter')
        if fork_elem:
            stats['forks'] = fork_elem.get_text(strip=True)
        
        # 主要编程语言
        lang_elem = soup.select_one('[itemprop="programmingLanguage"]')
        if lang_elem:
            stats['language'] = lang_elem.get_text(strip=True)
        
        return {'stats': stats}

    def _extract_product_specific(self, soup: BeautifulSoup) -> Dict:
        """提取产品网站特有的信息"""
        return {
            'votes': self._extract_by_selectors(soup, 'votes'),
            'maker': self._extract_multiple_by_selectors(soup, 'author')[:3]  # 限制制作者数量
        }

    def _error_result(self, url: str, error_msg: str) -> Dict:
        """生成错误结果"""
        return {
            'url': url,
            'status': 'error',
            'error': error_msg
        }

    def save_to_file(self, content: str, filename: str, content_type: str = 'html') -> bool:
        """
        保存内容到文件
        支持HTML和文本两种格式
        """
        try:
            if content_type == 'html':
                html_content = self._prepare_html_content(content)
                with open(f"{filename}.html", 'w', encoding='utf-8-sig') as f:
                    f.write(html_content)
                print(f"✅ HTML内容已保存到 {filename}.html")
            else:
                with open(f"{filename}.txt", 'w', encoding='utf-8-sig') as f:
                    f.write(content)
                print(f"✅ 文本内容已保存到 {filename}.txt")
            return True
        except Exception as e:
            print(f"❌ 保存文件失败: {str(e)}")
            return False

    def _prepare_html_content(self, content: str) -> str:
        """
        准备HTML内容，添加完整的HTML结构和样式
        这是所有demo中最优雅的样式汇总
        """
        if content.strip().startswith('<!DOCTYPE') or content.strip().startswith('<html'):
            return content

        html_template = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>提取的网页内容</title>
    <style>
        /* 通用样式 - 汇总所有demo的最佳实践 */
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
            background-color: #fff;
        }}

        /* 标题样式 */
        h1, h2, h3, h4, h5, h6 {{
            color: #2c3e50;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
            font-weight: 600;
        }}

        h1 {{ font-size: 2.2em; border-bottom: 2px solid #3498db; padding-bottom: 10px; }}
        h2 {{ font-size: 1.8em; }}
        h3 {{ font-size: 1.4em; }}

        /* 段落和文本 */
        p {{
            margin-bottom: 1em;
            text-align: justify;
        }}

        /* 代码样式 */
        code {{
            background-color: #f1f2f6;
            color: #e74c3c;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', monospace;
            font-size: 0.9em;
        }}

        pre {{
            background-color: #2d3748;
            color: #e2e8f0;
            padding: 16px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 1em 0;
            line-height: 1.4;
        }}

        pre code {{
            background: none;
            color: inherit;
            padding: 0;
        }}

        /* 引用样式 */
        blockquote {{
            border-left: 4px solid #3498db;
            padding-left: 16px;
            margin: 1em 0;
            color: #7f8c8d;
            font-style: italic;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 0 4px 4px 0;
        }}

        /* 图片样式 */
        img {{
            max-width: 100%;
            height: auto;
            border-radius: 6px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
            margin: 1em 0;
        }}

        /* 表格样式 */
        table {{
            border-collapse: collapse;
            width: 100%;
            margin: 1em 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }}

        th, td {{
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }}

        th {{
            background-color: #f8f9fa;
            font-weight: bold;
            color: #2c3e50;
        }}

        tr:nth-child(even) {{
            background-color: #f8f9fa;
        }}

        /* 链接样式 */
        a {{
            color: #3498db;
            text-decoration: none;
            border-bottom: 1px solid transparent;
            transition: all 0.3s ease;
        }}

        a:hover {{
            border-bottom-color: #3498db;
        }}

        /* 列表样式 */
        ul, ol {{
            padding-left: 2em;
            margin: 1em 0;
        }}

        li {{
            margin-bottom: 0.5em;
        }}

        /* 强调样式 */
        strong {{
            color: #2c3e50;
            font-weight: 600;
        }}

        em {{
            color: #7f8c8d;
        }}

        /* 分隔线 */
        hr {{
            border: none;
            height: 2px;
            background: linear-gradient(to right, #3498db, #2ecc71);
            margin: 2em 0;
            border-radius: 1px;
        }}

        /* 响应式设计 */
        @media (max-width: 768px) {{
            body {{
                padding: 15px;
            }}
            
            h1 {{
                font-size: 1.8em;
            }}
            
            table {{
                font-size: 0.9em;
            }}
        }}
    </style>
</head>
<body>
{content}
</body>
</html>"""
        return html_template

    def extract_and_save(self, url: str, filename_prefix: str = "extracted_content", 
                        site_type: str = 'auto') -> Dict:
        """
        完整的提取和保存流程
        
        Args:
            url (str): 要提取的网页链接
            filename_prefix (str): 保存文件的前缀名
            site_type (str): 网站类型提示
            
        Returns:
            dict: 提取结果
        """
        print("=" * 70)
        print("🌟 通用HTML内容提取器")
        print("=" * 70)
        
        # 提取内容
        result = self.extract_content(url, site_type)
        
        if result['status'] == 'success':
            print(f"\n✅ 内容提取成功！")
            print(f"📝 标题: {result['title']}")
            print(f"👤 作者: {result['author']}")
            print(f"📅 时间: {result['publish_time']}")
            print(f"🔖 描述: {result['description']}")
            print(f"🏷️ 标签: {', '.join(result['tags'][:5])}")
            print(f"📄 HTML长度: {len(result['content_html'])} 字符")
            print(f"📝 文本长度: {len(result['content_text'])} 字符")
            print(f"🔤 编码: {result['encoding']}")
            print(f"🌐 网站类型: {result['site_type']}")
            
            print(f"\n💾 正在保存文件...")
            
            # 保存HTML内容
            self.save_to_file(result['content_html'], filename_prefix, 'html')
            
            # 保存纯文本内容
            text_summary = self._create_text_summary(result)
            self.save_to_file(text_summary, filename_prefix, 'text')
            
            # 显示预览
            print(f"\n📖 内容预览 (前200字符):")
            print("-" * 50)
            preview = result['content_text'][:200].replace('\n', ' ')
            print(f"{preview}...")
            print("-" * 50)
            
        else:
            print(f"\n❌ 提取失败: {result['error']}")
            print("\n💡 建议:")
            print("1. 检查网络连接")
            print("2. 确认URL是否正确")
            print("3. 尝试添加自定义请求头")
        
        return result

    def _create_text_summary(self, result: Dict) -> str:
        """创建文本摘要"""
        summary = f"""网页提取摘要
{'='*50}

标题: {result['title']}
作者: {result['author']}
发布时间: {result['publish_time']}
网站类型: {result['site_type']}
编码格式: {result['encoding']}

描述: {result['description']}

标签: {', '.join(result['tags']) if result['tags'] else '无'}

{'='*50}
正文内容:
{'='*50}

{result['content_text']}

{'='*50}
原始链接: {result['url']}
提取时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
"""
        return summary


def main():
    """主函数 - 使用示例"""
    # 创建提取器实例
    extractor = UniversalHTMLExtractor()
    
    # 测试不同类型的网站
    test_urls = [
        # 可以添加您想测试的URL
        "https://docs.weblibre.eu/"
    ]
    
    for i, url in enumerate(test_urls, 1):
        print(f"\n🧪 测试 {i}: {url}")
        result = extractor.extract_and_save(url, f"test_{i}")
        
        if result['status'] == 'success':
            print("✅ 测试成功")
        else:
            print("❌ 测试失败")
        
        time.sleep(2)  # 避免请求过快


if __name__ == "__main__":
    main()
