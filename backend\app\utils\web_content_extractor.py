import uuid

import requests
from bs4 import BeautifulSoup
import re
from urllib.parse import urlparse
import time
import chardet
import structlog

logger = structlog.get_logger(__name__)


class WebContentExtractor:
    """统一的网页内容提取器，支持多个平台"""

    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'max-age=0'
        }




    def __del__(self):
        """析构函数，确保资源释放"""
        if hasattr(self, '_container') and self._container:
            try:
                # 清理容器资源
                if hasattr(self._container, 'cleanup'):
                    self._container.cleanup()
            except Exception:
                pass

    def extract_content(self, url):
        """
        根据URL自动识别平台并提取内容

        Args:
            url (str): 网页链接

        Returns:
            str: 格式化的HTML字符串，失败时返回None
        """
        try:
            platform = self._identify_platform(url)
            logger.info(f"识别平台: {platform}")

            # 根据平台选择对应的提取方法
            if platform == 'juejin':
                return self._extract_juejin(url)
            elif platform == 'arxiv':
                return self._extract_arxiv(url)
            elif platform == 'devto':
                return self._extract_devto(url)
            elif platform == 'reddit':
                return self._extract_reddit(url)
            elif platform == 'csdn':
                return self._extract_csdn(url)
            elif platform == 'github':
                return self._extract_github(url)
            elif platform == 'oschina':
                return self._extract_oschina(url)
            elif platform == 'producthunt':
                return self._extract_producthunt(url)
            elif platform == 'stackoverflow':
                return self._extract_stackoverflow(url)
            else:
                # 新增：对于不支持的网站，尝试通用解析
                logger.info(f"不支持的平台: {platform}，尝试通用解析")
                return self._extract_generic_website(url)

        except Exception as e:
            logger.error(f"提取内容失败: {str(e)}")
            return None

    def _identify_platform(self, url):
        """识别URL对应的平台"""



        parsed_url = urlparse(url.lower())
        domain = parsed_url.netloc

        if 'juejin.cn' in domain:
            return 'juejin'
        elif 'arxiv.org' in domain:
            return 'arxiv'
        elif 'dev.to' in domain:
            return 'devto'
        elif 'reddit.com' in domain:
            return 'reddit'
        elif 'csdn.net' in domain:
            return 'csdn'
        # 新增平台识别
        elif 'github.com' in domain:
            return 'github'
        elif 'oschina.net' in domain:
            return 'oschina'
        elif 'producthunt.com' in domain:
            return 'producthunt'
        elif 'stackoverflow.com' in domain:
            return 'stackoverflow'
        else:
            return 'unknown'

    def _extract_juejin(self, url):
        """提取掘金文章内容"""
        response = requests.get(url, headers=self.headers, timeout=15)
        response.raise_for_status()

        encoding = self._detect_encoding(response)
        response.encoding = encoding

        soup = BeautifulSoup(response.text, 'html.parser')

        # 提取标题
        title = self._extract_title_juejin(soup)

        # 提取内容
        content = self._extract_content_juejin(soup)

        return self._prepare_html_content(content, title)

    def _extract_arxiv(self, url):
        """提取arXiv论文内容"""
        response = requests.get(url, headers=self.headers)
        response.raise_for_status()

        soup = BeautifulSoup(response.content, 'html.parser')

        # 清理SVG元素
        self._clean_svg_elements(soup)

        # 提取论文信息
        title = ""
        content_parts = []

        # 提取标题
        title_element = soup.find('h1', class_='title')
        if title_element:
            title = title_element.get_text(strip=True)
            if title.startswith('Title:'):
                title = title[6:].strip()

        # 提取作者
        authors_element = soup.find('div', class_='authors')
        if authors_element:
            authors = authors_element.get_text(strip=True)
            if authors.startswith('Authors: <AUTHORS>
                authors = authors[8:].strip()
            content_parts.append(f"<p><strong>作者:</strong> {authors}</p>")

        # 提取摘要
        abstract_element = soup.find('blockquote', class_='abstract')
        if abstract_element:
            abstract = abstract_element.get_text(strip=True)
            if abstract.startswith('Abstract:'):
                abstract = abstract[9:].strip()
            content_parts.append(f"<div><strong>摘要:</strong><br>{abstract}</div>")

        content = "".join(content_parts)
        return self._prepare_html_content(content, title)

    def _extract_devto(self, url):
        """提取dev.to文章内容"""
        response = requests.get(url, headers=self.headers)
        response.raise_for_status()
        response.encoding = 'utf-8'

        soup = BeautifulSoup(response.text, 'html.parser')

        # 移除不需要的元素
        for element in soup(['script', 'style', 'nav', 'header', 'footer', 'aside']):
            element.decompose()

        # 查找文章内容
        selectors = [
            'article',
            '.crayons-article__main',
            '[data-article-content]',
            '.article-body',
            '#article-body',
            'main',
        ]

        article_content = None
        for selector in selectors:
            article_content = soup.select_one(selector)
            if article_content:
                break

        if not article_content:
            # 查找包含最多文本的div
            divs = soup.find_all('div')
            max_text_length = 0
            for div in divs:
                text_length = len(div.get_text(strip=True))
                if text_length > max_text_length:
                    max_text_length = text_length
                    article_content = div

        if not article_content:
            article_content = soup.find('body') or soup

        # 清理内容
        for unwanted in article_content.find_all(['div'],
                                                 class_=re.compile('comment|reply|ad|sponsor|promo|share|social')):
            unwanted.decompose()

        # 提取标题
        title_elem = soup.find('h1')
        title = title_elem.get_text(strip=True) if title_elem else "Dev.to文章"

        return self._prepare_html_content(str(article_content), title)

    def _extract_reddit(self, url):
        """提取Reddit内容"""
        # 转换为Old Reddit URL
        old_url = self._convert_to_old_reddit(url)

        time.sleep(1)  # 避免请求过快
        response = requests.get(old_url, headers=self.headers, timeout=10)
        response.raise_for_status()

        soup = BeautifulSoup(response.content, 'html.parser')

        # 提取标题
        title = ""
        title_selectors = [
            'a.title.may-blank',
            '.thing .entry .title a',
            '.submission .entry .title a',
            'p.title a',
        ]

        for selector in title_selectors:
            title_element = soup.select_one(selector)
            if title_element:
                title = title_element.get_text().strip()
                break

        # 提取正文内容
        content = ""
        content_selectors = [
            '.thing .entry .usertext-body .md',
            '.submission .usertext-body .md',
            '.self .usertext-body .md',
            '.entry .usertext .md',
            '.usertext-body .md',
        ]

        for selector in content_selectors:
            content_element = soup.select_one(selector)
            if content_element:
                content = str(content_element)
                break

        if not content:
            content = "<p>这是一个链接帖子或图片帖子，没有文字正文</p>"

        return self._prepare_html_content(content, title)

    def _extract_csdn(self, url):
        """提取CSDN文章内容"""
        response = requests.get(url, headers=self.headers)
        response.raise_for_status()

        soup = BeautifulSoup(response.text, 'html.parser')

        # 提取标题
        title_element = soup.find('h1', class_='title-article') or soup.find('h1', class_='title')
        title = title_element.get_text().strip() if title_element else "CSDN文章"

        # 提取正文内容
        content_selectors = [
            'div#content_views',
            'div.article_content',
            'div.htmledit_views',
            'article',
            'div.blog-content-box'
        ]

        article_content = None
        for selector in content_selectors:
            article_content = soup.select_one(selector)
            if article_content:
                break

        if not article_content:
            # 查找包含大量文本的div
            all_divs = soup.find_all('div')
            for div in all_divs:
                if div.get_text() and len(div.get_text().strip()) > 500:
                    article_content = div
                    break

        if article_content:
            # 清理内容
            unwanted_elements = article_content.find_all(['script', 'style', 'iframe'])
            for element in unwanted_elements:
                element.decompose()

            unwanted_classes = ['csdn-side-toolbar', 'tool-box', 'recommend-box', 'comment-box']
            for class_name in unwanted_classes:
                unwanted = article_content.find_all(class_=class_name)
                for element in unwanted:
                    element.decompose()

            content = str(article_content)
        else:
            content = "<p>未能提取到文章内容</p>"

        return self._prepare_html_content(content, title)

    def _extract_github(self, url):
        """提取GitHub仓库内容"""
        try:
            response = requests.get(url, headers=self.headers, timeout=15)
            response.raise_for_status()

            encoding = self._detect_encoding(response)
            response.encoding = encoding

            soup = BeautifulSoup(response.text, 'html.parser')

            # 提取仓库标题
            title = self._extract_github_title(soup)

            # 提取仓库描述
            description = self._extract_github_description(soup)

            # 提取README内容
            readme_content = self._extract_github_readme(soup)

            # 提取统计信息
            stats = self._extract_github_stats(soup)

            # 组合内容
            content_parts = []
            if description:
                content_parts.append(f"<div class='description'><strong>项目描述:</strong> {description}</div>")

            if stats:
                stats_html = "<div class='stats'><strong>项目统计:</strong><ul>"
                for key, value in stats.items():
                    stats_html += f"<li>{key}: {value}</li>"
                stats_html += "</ul></div>"
                content_parts.append(stats_html)

            if readme_content:
                content_parts.append(f"<div class='readme'>{readme_content}</div>")

            content = "".join(content_parts) if content_parts else "<p>未找到项目内容</p>"

            return self._prepare_html_content(content, title)

        except Exception as e:
            logger.error(f"GitHub内容提取失败: {str(e)}")
            return self._prepare_html_content("<p>GitHub内容提取失败</p>", "GitHub项目")

    def _extract_github_title(self, soup):
        """提取GitHub仓库标题"""
        title_selectors = [
            'h1[itemprop="name"] a',
            'h1.public strong a',
            '.repository-content h1 strong a',
            'h1 strong a',
            'h1',
            'title'
        ]

        for selector in title_selectors:
            title_elem = soup.select_one(selector)
            if title_elem:
                title = title_elem.get_text(strip=True)
                if title and len(title) > 2:
                    return title

        return "GitHub项目"

    def _extract_github_description(self, soup):
        """提取GitHub仓库描述"""
        description_selectors = [
            '[itemprop="about"]',
            '.repository-content p',
            '.BorderGrid-cell p',
            '.f4.my-3'
        ]

        for selector in description_selectors:
            desc_elem = soup.select_one(selector)
            if desc_elem:
                description = desc_elem.get_text(strip=True)
                if description and len(description) > 10:
                    return description

        return None

    def _extract_github_readme(self, soup):
        """提取GitHub README内容"""
        readme_selectors = [
            'article[itemprop="text"]',
            '.Box-body.px-5.pb-5',
            '.markdown-body',
            '#readme article',
            '.repository-content .markdown-body'
        ]

        for selector in readme_selectors:
            readme_elem = soup.select_one(selector)
            if readme_elem:
                # 清理内容
                self._clean_github_content(readme_elem)

                # 检查内容是否有意义
                text_content = readme_elem.get_text(strip=True)
                if len(text_content) > 100:
                    return str(readme_elem)

        return None

    def _extract_github_stats(self, soup):
        """提取GitHub统计信息"""
        stats = {}

        # 提取星标数
        star_elem = soup.select_one('#repo-stars-counter-star')
        if star_elem:
            stats['星标数'] = star_elem.get_text(strip=True)

        # 提取Fork数
        fork_elem = soup.select_one('#repo-network-counter')
        if fork_elem:
            stats['Fork数'] = fork_elem.get_text(strip=True)

        # 提取语言信息
        lang_elem = soup.select_one('[itemprop="programmingLanguage"]')
        if lang_elem:
            stats['主要语言'] = lang_elem.get_text(strip=True)

        return stats

    def _clean_github_content(self, content_elem):
        """清理GitHub内容"""
        # 移除脚本和样式标签
        for script in content_elem(["script", "style", "noscript"]):
            script.decompose()

        # 移除GitHub特定的无关元素
        unwanted_selectors = [
            '.js-file-line-container',
            '.btn',
            '.btn-group',
            '.pagination',
            '.breadcrumb',
            '.avatar',
            '.social-count',
            '.js-social-form'
        ]

        for selector in unwanted_selectors:
            for elem in content_elem.select(selector):
                elem.decompose()

    def _extract_oschina(self, url):
        """提取开源中国新闻内容"""
        try:
            response = requests.get(url, headers=self.headers, timeout=15)
            response.raise_for_status()

            encoding = self._detect_encoding(response)
            response.encoding = encoding

            soup = BeautifulSoup(response.text, 'html.parser')

            # 提取新闻标题
            title = self._extract_oschina_title(soup)

            # 提取新闻正文
            content = self._extract_oschina_content(soup)

            # 提取作者信息
            author = self._extract_oschina_author(soup)

            # 提取发布时间
            publish_time = self._extract_oschina_time(soup)

            # 组合内容
            content_parts = []
            if author:
                content_parts.append(f"<div class='author'><strong>作者:</strong> {author}</div>")
            if publish_time:
                content_parts.append(f"<div class='time'><strong>发布时间:</strong> {publish_time}</div>")
            if content:
                content_parts.append(f"<div class='content'>{content}</div>")

            full_content = "".join(content_parts) if content_parts else "<p>未找到新闻内容</p>"

            return self._prepare_html_content(full_content, title)

        except Exception as e:
            logger.error(f"开源中国内容提取失败: {str(e)}")
            return self._prepare_html_content("<p>开源中国内容提取失败</p>", "开源中国新闻")

    def _extract_oschina_title(self, soup):
        """提取开源中国新闻标题"""
        title_selectors = [
            'h1.article-title',
            '.article-detail h1',
            '.news-title h1',
            'h1.title',
            '.entry-title',
            'h1'
        ]

        for selector in title_selectors:
            title_elem = soup.select_one(selector)
            if title_elem:
                title = title_elem.get_text(strip=True)
                if title and len(title) > 5:
                    return title

        return "开源中国新闻"

    def _extract_oschina_content(self, soup):
        """提取开源中国新闻正文"""
        content_selectors = [
            '.article-detail .content',
            '.news-content',
            '.article-content',
            '.entry-content',
            '.post-content',
            '.content',
            'article'
        ]

        for selector in content_selectors:
            content_elem = soup.select_one(selector)
            if content_elem:
                content_copy = content_elem.__copy__()
                self._clean_oschina_content(content_copy)

                text_content = content_copy.get_text(strip=True)
                if len(text_content) > 100:
                    return str(content_copy)

        return None

    def _extract_oschina_author(self, soup):
        """提取开源中国作者信息"""
        author_selectors = [
            '.author-name',
            '.news-author',
            '.article-author',
            '.author',
            '[rel="author"]',
            '.byline .author'
        ]

        for selector in author_selectors:
            author_elem = soup.select_one(selector)
            if author_elem:
                return author_elem.get_text(strip=True)

        return None

    def _extract_oschina_time(self, soup):
        """提取开源中国发布时间"""
        time_selectors = [
            '.publish-time',
            '.article-time',
            '.news-date',
            'time',
            '.date',
            '.time'
        ]

        for selector in time_selectors:
            time_elem = soup.select_one(selector)
            if time_elem:
                if time_elem.has_attr('datetime'):
                    return time_elem['datetime']
                time_text = time_elem.get_text(strip=True)
                if time_text:
                    return time_text

        return None

    def _clean_oschina_content(self, content_elem):
        """清理开源中国内容"""
        # 移除脚本和样式
        for script in content_elem(["script", "style", "noscript"]):
            script.decompose()

        # 移除开源中国特定的无关元素
        unwanted_selectors = [
            '.ad', '.advertisement', '.ads',
            '.share', '.social-share',
            '.comment', '.comments',
            '.related', '.recommend',
            '.sidebar', '.widget',
            '.footer', '.header',
            '.nav', '.navigation',
            '.breadcrumb', '.pagination',
            '.tags', '.tag-list',
            '.author-info', '.author-card'
        ]

        for selector in unwanted_selectors:
            for elem in content_elem.select(selector):
                elem.decompose()

    def _extract_producthunt(self, url):
        """提取ProductHunt产品信息"""
        try:
            response = requests.get(url, headers=self.headers, timeout=15)
            response.raise_for_status()

            encoding = self._detect_encoding(response)
            response.encoding = encoding

            soup = BeautifulSoup(response.text, 'html.parser')

            # 提取产品名称
            product_name = self._extract_producthunt_name(soup)

            # 提取产品描述
            description = self._extract_producthunt_description(soup)

            # 提取产品标签
            tags = self._extract_producthunt_tags(soup)

            # 提取制作团队
            maker = self._extract_producthunt_maker(soup)

            # 提取点赞数
            votes = self._extract_producthunt_votes(soup)

            # 组合内容
            content_parts = []
            if description:
                content_parts.append(f"<div class='description'><strong>产品描述:</strong> {description}</div>")

            if maker:
                content_parts.append(f"<div class='maker'><strong>制作团队:</strong> {', '.join(maker)}</div>")

            if votes:
                content_parts.append(f"<div class='votes'><strong>点赞数:</strong> {votes}</div>")

            if tags:
                tags_html = "<div class='tags'><strong>产品标签:</strong> "
                tags_html += " ".join([f"<span class='tag'>{tag}</span>" for tag in tags])
                tags_html += "</div>"
                content_parts.append(tags_html)

            content = "".join(content_parts) if content_parts else "<p>未找到产品信息</p>"

            return self._prepare_html_content(content, product_name)

        except Exception as e:
            logger.error(f"ProductHunt内容提取失败: {str(e)}")
            return self._prepare_html_content("<p>ProductHunt内容提取失败</p>", "ProductHunt产品")

    def _extract_producthunt_name(self, soup):
        """提取ProductHunt产品名称"""
        name_selectors = [
            'h1[data-test="product-name"]',
            'h1.product-name',
            'h1[class*="product"]',
            '.product-header h1',
            'h1',
            'title'
        ]

        for selector in name_selectors:
            name_elem = soup.select_one(selector)
            if name_elem:
                name = name_elem.get_text(strip=True)
                if name and len(name) > 2 and not name.startswith('Product Hunt'):
                    return name

        return "ProductHunt产品"

    def _extract_producthunt_description(self, soup):
        """提取ProductHunt产品描述"""
        desc_selectors = [
            '[data-test="product-description"]',
            '.product-description',
            '.tagline',
            '.description',
            '[class*="description"]',
            '.subtitle'
        ]

        for selector in desc_selectors:
            desc_elem = soup.select_one(selector)
            if desc_elem:
                description = desc_elem.get_text(strip=True)
                if description and len(description) > 10:
                    return description

        return None

    def _extract_producthunt_tags(self, soup):
        """提取ProductHunt产品标签"""
        tags = []
        tag_selectors = [
            '.tag',
            '.topic',
            '.category',
            '[data-test="topic"]',
            '.badge',
            '.label'
        ]

        for selector in tag_selectors:
            tag_elems = soup.select(selector)
            for elem in tag_elems:
                tag = elem.get_text(strip=True)
                if tag and len(tag) < 50 and tag not in tags:
                    tags.append(tag)

        return tags[:10]  # 限制标签数量

    def _extract_producthunt_maker(self, soup):
        """提取ProductHunt制作团队信息"""
        maker_selectors = [
            '[data-test="maker"]',
            '.maker',
            '.creator',
            '.author',
            '.team',
            '.hunter'
        ]

        makers = []
        for selector in maker_selectors:
            maker_elems = soup.select(selector)
            for elem in maker_elems:
                maker = elem.get_text(strip=True)
                if maker and len(maker) < 100 and maker not in makers:
                    makers.append(maker)

        return makers if makers else ["未知制作者"]

    def _extract_producthunt_votes(self, soup):
        """提取ProductHunt点赞数"""
        vote_selectors = [
            '[data-test="vote-count"]',
            '.vote-count',
            '.upvotes',
            '.votes',
            '[class*="vote"]'
        ]

        for selector in vote_selectors:
            vote_elem = soup.select_one(selector)
            if vote_elem:
                votes = vote_elem.get_text(strip=True)
                vote_numbers = re.findall(r'\d+', votes)
                if vote_numbers:
                    return vote_numbers[0]

        return "0"



    def _clean_news_content(self, content: str) -> str:
        """
        清理新闻内容，确保HTML安全
        
        Args:
            content (str): 原始新闻内容
            
        Returns:
            str: 清理后的安全HTML内容
        """
        try:
            # 使用 BeautifulSoup 清理内容
            soup = BeautifulSoup(content, 'html.parser')
            
            # 移除潜在的恶意脚本
            for script in soup(["script", "style", "iframe", "object", "embed"]):
                script.decompose()
            
            # 移除事件处理器属性
            for tag in soup.find_all():
                # 移除所有以 on 开头的事件属性
                attrs_to_remove = []
                for attr in tag.attrs:
                    if attr.startswith('on') or attr.startswith('javascript:'):
                        attrs_to_remove.append(attr)
                
                for attr in attrs_to_remove:
                    del tag[attr]
            
            # 只保留安全的HTML标签
            allowed_tags = [
                'p', 'div', 'span', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
                'ul', 'ol', 'li', 'strong', 'em', 'b', 'i', 'u', 'br', 'hr',
                'blockquote', 'pre', 'code', 'a', 'img', 'table', 'tr', 'td', 'th'
            ]
            
            # 移除不在允许列表中的标签，但保留其内容
            for tag in soup.find_all():
                if tag.name not in allowed_tags:
                    # 保留标签内容，但移除标签本身
                    tag.unwrap()
            
            # 清理链接，确保安全性
            for link in soup.find_all('a'):
                if link.get('href'):
                    href = link['href']
                    # 只允许 http 和 https 链接
                    if not href.startswith(('http://', 'https://')):
                        link['href'] = '#'
                        link['title'] = '链接已禁用'
            
            # 清理图片，确保安全性
            for img in soup.find_all('img'):
                src = img.get('src')
                if src:
                    # 只允许 http 和 https 图片
                    if not src.startswith(('http://', 'https://')):
                        img.decompose()
                    else:
                        # 添加安全属性
                        img['loading'] = 'lazy'
                        img['referrerpolicy'] = 'no-referrer'
            
            return str(soup)
            
        except Exception as e:
            logger.error(f"清理新闻内容失败: {str(e)}")
            # 如果清理失败，返回纯文本内容
            return f"<p>{content}</p>"

    def _extract_stackoverflow(self, url):
        """提取StackOverflow问答内容"""
        try:
            response = requests.get(url, headers=self.headers, timeout=15)
            response.raise_for_status()

            encoding = self._detect_encoding(response)
            response.encoding = encoding

            soup = BeautifulSoup(response.text, 'html.parser')

            # 提取问题标题
            title = self._extract_stackoverflow_title(soup)

            # 提取问题内容
            question_content = self._extract_stackoverflow_question(soup)

            # 提取问题标签
            tags = self._extract_stackoverflow_tags(soup)

            # 提取投票数
            votes = self._extract_stackoverflow_votes(soup)

            # 提取所有答案
            answers = self._extract_stackoverflow_answers(soup)

            # 组合内容
            content_parts = []
            if votes:
                content_parts.append(f"<div class='votes'><strong>投票数:</strong> {votes}</div>")

            if tags:
                tags_html = "<div class='tags'><strong>问题标签:</strong> "
                tags_html += " ".join([f"<span class='tag'>{tag}</span>" for tag in tags])
                tags_html += "</div>"
                content_parts.append(tags_html)

            if question_content:
                content_parts.append(f"<div class='question'><h3>问题内容:</h3>{question_content}</div>")

            if answers:
                content_parts.append("<div class='answers'><h3>答案:</h3>")
                for i, answer in enumerate(answers[:3], 1):  # 只显示前3个答案
                    content_parts.append(f"<div class='answer'><h4>答案 {i}:</h4>{answer['content']}</div>")
                content_parts.append("</div>")

            content = "".join(content_parts) if content_parts else "<p>未找到问答内容</p>"

            return self._prepare_html_content(content, title)

        except Exception as e:
            logger.error(f"StackOverflow内容提取失败: {str(e)}")
            return self._prepare_html_content("<p>StackOverflow内容提取失败</p>", "StackOverflow问答")

    def _extract_stackoverflow_title(self, soup):
        """提取StackOverflow问题标题"""
        title_selectors = [
            'h1[itemprop="name"] a',
            'h1.question-title',
            '.question-header h1',
            'h1',
            'title'
        ]

        for selector in title_selectors:
            title_elem = soup.select_one(selector)
            if title_elem:
                title = title_elem.get_text(strip=True)
                if title and len(title) > 5 and not title.startswith('Stack Overflow'):
                    return title

        return "StackOverflow问题"

    def _extract_stackoverflow_question(self, soup):
        """提取StackOverflow问题内容"""
        question_selectors = [
            '.question .s-prose',
            '.question .post-text',
            '.question .postcell',
            '[itemprop="text"]'
        ]

        for selector in question_selectors:
            question_elem = soup.select_one(selector)
            if question_elem:
                content_copy = question_elem.__copy__()
                self._clean_stackoverflow_content(content_copy)

                text = content_copy.get_text(strip=True)
                if len(text) > 50:
                    return str(content_copy)

        return None

    def _extract_stackoverflow_tags(self, soup):
        """提取StackOverflow问题标签"""
        tags = []
        tag_selectors = [
            '.question .post-tag',
            '.post-taglist .post-tag',
            '.tags .post-tag'
        ]

        for selector in tag_selectors:
            tag_elems = soup.select(selector)
            for elem in tag_elems:
                tag = elem.get_text(strip=True)
                if tag and tag not in tags:
                    tags.append(tag)

        return tags

    def _extract_stackoverflow_votes(self, soup):
        """提取StackOverflow问题投票数"""
        vote_selectors = [
            '.question .js-vote-count',
            '.question .vote-count-post',
            '.question [data-value]'
        ]

        for selector in vote_selectors:
            vote_elem = soup.select_one(selector)
            if vote_elem:
                if vote_elem.has_attr('data-value'):
                    return vote_elem['data-value']
                votes = vote_elem.get_text(strip=True)
                if votes.isdigit() or votes.startswith('-'):
                    return votes

        return "0"

    def _extract_stackoverflow_answers(self, soup):
        """提取StackOverflow答案"""
        answers = []
        answer_selectors = [
            '.answer',
            '[id^="answer-"]'
        ]

        for selector in answer_selectors:
            answer_elems = soup.select(selector)
            for elem in answer_elems[:5]:  # 限制答案数量
                answer_data = self._parse_stackoverflow_answer(elem)
                if answer_data:
                    answers.append(answer_data)

        return answers

    def _parse_stackoverflow_answer(self, answer_elem):
        """解析单个StackOverflow答案"""
        try:
            # 答案内容
            content_elem = answer_elem.select_one('.s-prose, .post-text, .postcell')
            if not content_elem:
                return None

            content_copy = content_elem.__copy__()
            self._clean_stackoverflow_content(content_copy)
            content = str(content_copy)

            # 答案投票数
            vote_elem = answer_elem.select_one('.js-vote-count, .vote-count-post, [data-value]')
            votes = "0"
            if vote_elem:
                if vote_elem.has_attr('data-value'):
                    votes = vote_elem['data-value']
                else:
                    vote_text = vote_elem.get_text(strip=True)
                    if vote_text.isdigit() or vote_text.startswith('-'):
                        votes = vote_text

            return {
                'content': content,
                'votes': votes
            }

        except Exception as e:
            logger.error(f"解析StackOverflow答案时出错: {e}")
            return None

    def _clean_stackoverflow_content(self, content_elem):
        """清理StackOverflow内容"""
        # 移除脚本和样式
        for script in content_elem(["script", "style", "noscript"]):
            script.decompose()

        # 移除StackOverflow特定的无关元素
        unwanted_selectors = [
            '.js-vote-count',
            '.vote-count-post',
            '.js-favorite-btn',
            '.bookmark-btn',
            '.js-share-link',
            '.js-flag-post-link',
            '.js-comment-link',
            '.js-edit-post',
            '.community-option',
            '.post-menu',
            '.post-signature'
        ]

        for selector in unwanted_selectors:
            for elem in content_elem.select(selector):
                elem.decompose()

    def _detect_encoding(self, response):
        """智能检测网页编码"""
        # 从HTTP响应头获取编码
        content_type = response.headers.get('content-type', '').lower()
        if 'charset=' in content_type:
            charset = content_type.split('charset=')[1].split(';')[0].strip()
            if charset:
                return charset

        # 从HTML meta标签获取编码
        try:
            partial_soup = BeautifulSoup(response.content[:4096], 'html.parser')

            meta_charset = partial_soup.find('meta', attrs={'charset': True})
            if meta_charset:
                return meta_charset['charset']

            meta_http_equiv = partial_soup.find('meta', attrs={'http-equiv': re.compile('Content-Type', re.I)})
            if meta_http_equiv and 'content' in meta_http_equiv.attrs:
                content = meta_http_equiv['content']
                if 'charset=' in content:
                    return content.split('charset=')[1].split(';')[0].strip()
        except Exception:
            pass

        # 使用chardet库检测编码
        try:
            detected = chardet.detect(response.content)
            if detected['confidence'] > 0.7:
                return detected['encoding']
        except Exception:
            pass

        return 'utf-8'

    def _extract_title_juejin(self, soup):
        """提取掘金标题"""
        title_selectors = [
            'h1.article-title',
            '.article-title',
            'h1[data-id="article-title"]',
            'h1.entry-title',
            '.entry-title',
            'h1',
            'title'
        ]

        for selector in title_selectors:
            title_elem = soup.select_one(selector)
            if title_elem:
                title = title_elem.get_text(strip=True)
                if title and len(title) > 5:
                    return title

        return "掘金文章"

    def _extract_content_juejin(self, soup):
        """提取掘金正文"""
        content_selectors = [
            '.markdown-body',
            '.article-content',
            '.entry-content',
            '.post-content',
            '.article-body',
            '.content',
            'article',
            '.article'
        ]

        for selector in content_selectors:
            content_elem = soup.select_one(selector)
            if content_elem:
                content_copy = content_elem.__copy__()
                self._clean_content(content_copy)

                text_content = content_copy.get_text(strip=True)
                if len(text_content) > 100:
                    return str(content_copy)

        return "<p>未找到正文内容</p>"

    def _clean_content(self, content_elem):
        """清理HTML内容"""
        # 移除脚本和样式标签
        for script in content_elem(["script", "style", "noscript"]):
            script.decompose()

        # 移除无关元素
        unwanted_selectors = [
            '.advertisement', '.ad', '.ads', '.share-buttons', '.social-share',
            '.related-articles', '.comment-section', '.comments', '.author-info',
            '.article-meta', '.article-actions', '.follow-button', '.like-button',
            '.sidebar', '.footer', '.header', '.nav', '.navigation'
        ]

        for selector in unwanted_selectors:
            for elem in content_elem.select(selector):
                elem.decompose()

        # 移除空的标签
        for tag in content_elem.find_all():
            if not tag.get_text(strip=True) and not tag.find_all(['img', 'br', 'hr']):
                tag.decompose()

    def _clean_svg_elements(self, soup):
        """清理SVG元素"""
        for svg in soup.find_all('svg'):
            svg.decompose()

        for path in soup.find_all('path'):
            path.decompose()

        icon_selectors = [
            'i[class*="icon"]',
            'span[class*="icon"]',
            '.icon',
            '[class*="arrow"]',
            '[class*="button-icon"]'
        ]

        for selector in icon_selectors:
            for element in soup.select(selector):
                element.decompose()

    def _convert_to_old_reddit(self, url):
        """将普通Reddit URL转换为Old Reddit URL"""
        if 'www.reddit.com' in url:
            return url.replace('www.reddit.com', 'old.reddit.com')
        elif 'reddit.com' in url and 'old.reddit.com' not in url:
            return url.replace('reddit.com', 'old.reddit.com')
        return url

    def _prepare_html_content(self, content, title="文章"):
        """准备完整的HTML内容"""
        if content.strip().startswith('<!DOCTYPE') or content.strip().startswith('<html'):
            return content

        html_template = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <style>
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            color: #333;
            background-color: #fff;
        }}
        h1, h2, h3, h4, h5, h6 {{
            color: #2c3e50;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
        }}
        h1 {{ font-size: 2em; }}
        h2 {{ font-size: 1.5em; }}
        h3 {{ font-size: 1.2em; }}
        p {{ margin-bottom: 1em; }}
        code {{
            background-color: #f1f2f6;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9em;
            color: #e74c3c;
        }}
        pre {{
            background-color: #2d3748;
            color: #e2e8f0;
            padding: 16px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 1em 0;
        }}
        pre code {{
            background: none;
            color: inherit;
            padding: 0;
            font-size: 0.9em;
        }}
        blockquote {{
            border-left: 4px solid #3498db;
            padding-left: 16px;
            margin: 1em 0;
            color: #7f8c8d;
            font-style: italic;
        }}
        img {{
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }}
        table {{
            border-collapse: collapse;
            width: 100%;
            margin: 1em 0;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
        }}
        th {{
            background-color: #f8f9fa;
            font-weight: bold;
        }}
        a {{
            color: #3498db;
            text-decoration: none;
        }}
        a:hover {{
            text-decoration: underline;
        }}
        ul, ol {{ padding-left: 2em; }}
        li {{ margin-bottom: 0.5em; }}
    </style>
</head>
<body>
    <h1>{title}</h1>
    {content}
</body>
</html>"""
        return html_template

    def _extract_generic_website(self, url):
        """
        通用网站内容提取方法
        当遇到不支持的网站时，尝试智能提取主要内容

        Args:
            url (str): 网页链接

        Returns:
            str: 格式化的HTML字符串
        """
        try:
            # 获取网页内容
            response = requests.get(url, headers=self.headers, timeout=15)
            response.raise_for_status()

            # 智能检测编码
            encoding = self._detect_encoding(response)
            response.encoding = encoding

            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')

            # 提取标题
            title = self._extract_generic_title(soup)

            # 提取主要内容
            content = self._extract_generic_content(soup)

            # 如果没有找到内容，尝试备用方案
            if not content or len(content.get_text(strip=True)) < 100:
                content = self._extract_generic_content_fallback(soup)

            return self._prepare_html_content(str(content), title)

        except Exception as e:
            logger.error(f"通用网站内容提取失败: {str(e)}")
            return self._prepare_html_content("<p>内容提取失败</p>", "未知网站")

    def _extract_generic_title(self, soup):
        """
        提取通用网站标题
        """
        title_selectors = [
            'h1', 'h1.title', 'h1.entry-title', 'h1.post-title',
            '.title', '.entry-title', '.post-title', '.page-title',
            'title'  # 最后尝试HTML的title标签
        ]

        for selector in title_selectors:
            title_elem = soup.select_one(selector)
            if title_elem:
                title = title_elem.get_text(strip=True)
                if title and len(title) > 3 and len(title) < 200:
                    return title

        # 如果都没找到，返回页面标题
        page_title = soup.find('title')
        if page_title:
            return page_title.get_text(strip=True)

        return "网页内容"

    def _extract_generic_content(self, soup):
        """
        智能提取通用网站的主要内容
        """
        # 常见的文章内容选择器
        content_selectors = [
            'article', 'main', '.content', '.post-content', '.entry-content',
            '.article-content', '.post-body', '.entry-body', '.article-body',
            '.content-area', '.main-content', '.post-text', '.entry-text',
            '.article-text', '.content-main', '.main', '.primary'
        ]

        for selector in content_selectors:
            content_elem = soup.select_one(selector)
            if content_elem:
                # 检查内容是否有意义
                text_content = content_elem.get_text(strip=True)
                if len(text_content) > 200:  # 内容长度要足够
                    content_copy = content_elem.__copy__()
                    self._clean_generic_content(content_copy)
                    return content_copy

        return None

    def _extract_generic_content_fallback(self, soup):
        """
        备用内容提取方案：查找包含最多文本的div
        """
        # 移除明显的无关元素
        for unwanted in soup(['script', 'style', 'nav', 'header', 'footer', 'aside', 'sidebar']):
            unwanted.decompose()

        # 查找所有div，选择包含最多文本的那个
        divs = soup.find_all('div')
        best_div = None
        max_text_length = 0

        for div in divs:
            # 跳过明显不是内容的div
            if self._is_content_div(div):
                text_length = len(div.get_text(strip=True))
                if text_length > max_text_length:
                    max_text_length = text_length
                    best_div = div

        if best_div and max_text_length > 100:
            content_copy = best_div.__copy__()
            self._clean_generic_content(content_copy)
            return content_copy

        # 最后尝试body标签
        body = soup.find('body')
        if body:
            content_copy = body.__copy__()
            self._clean_generic_content(content_copy)
            return content_copy

        return soup.find('html') or soup

    def _is_content_div(self, div):
        """
        判断div是否可能是内容区域
        """
        # 跳过明显不是内容的div
        unwanted_classes = [
            'nav', 'navigation', 'menu', 'sidebar', 'footer', 'header',
            'comment', 'comments', 'ad', 'advertisement', 'ads',
            'share', 'social', 'related', 'recommend', 'widget',
            'breadcrumb', 'pagination', 'tags', 'meta'
        ]

        div_class = div.get('class', [])
        if isinstance(div_class, str):
            div_class = [div_class]

        for unwanted in unwanted_classes:
            if any(unwanted in cls.lower() for cls in div_class):
                return False

        return True

    def _clean_generic_content(self, content_elem):
        """
        清理通用网站内容
        """
        # 移除脚本和样式
        for script in content_elem(["script", "style", "noscript"]):
            script.decompose()

        # 移除常见的无关元素
        unwanted_selectors = [
            '.ad', '.advertisement', '.ads', '.banner',
            '.share', '.social-share', '.social-buttons',
            '.comment', '.comments', '.comment-section',
            '.related', '.recommend', '.suggested',
            '.sidebar', '.widget', '.side-content',
            '.footer', '.header', '.nav', '.navigation',
            '.breadcrumb', '.pagination', '.tags', '.tag-list',
            '.meta', '.post-meta', '.entry-meta',
            '.author', '.author-info', '.author-card'
        ]

        for selector in unwanted_selectors:
            for elem in content_elem.select(selector):
                elem.decompose()

        # 移除空的标签
        for tag in content_elem.find_all():
            if not tag.get_text(strip=True) and not tag.find_all(['img', 'br', 'hr']):
                tag.decompose()


# 使用示例
# def main():
#     """使用示例"""
#     extractor = WebContentExtractor()
#
#     # 测试URL列表
#     test_urls = [
#         "https://juejin.cn/post/7488210211380609051",
#         "https://arxiv.org/abs/1403.0025",
#         "https://dev.to/robbiecahill/what-is-a-webhook-api-a-deep-dive-for-developers-580a",
#         "https://www.reddit.com/r/China_irl/comments/1mx1jji/",
#         "https://blog.csdn.net/Attitude93/article/details/136671565"
#     ]
#
#     for url in test_urls:
#         logger.info(f"处理URL: {url}")
#         html_content = extractor.extract_content(url)
#         if html_content:
#             logger.info("提取成功")
#             # 可以保存到文件或直接返回HTML字符串
#         else:
#             logger.error("提取失败")
#
#
# if __name__ == "__main__":
#     main()