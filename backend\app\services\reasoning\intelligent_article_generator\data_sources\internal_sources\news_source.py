#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新闻数据源适配器 - 修复版本

基于本地新闻数据库作为数据源，使用异步数据库会话
"""

import logging
from typing import Any, Dict, List, Optional
from sqlalchemy import desc, select, func  # 使用 select 而不是 query
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime, timezone

from .internal_data_source import InternalDataSource
from ...models.data_source_models import DataSourceType, CollectedData, DataSourceConfig
from app.models.news.news import NewsModel
from app.core.di.providers import AsyncSessionProvider  # 导入异步会话提供者

logger = logging.getLogger(__name__)


class NewsSource(InternalDataSource):
    """新闻数据源适配器"""

    def __init__(self, config: Optional[DataSourceConfig] = None,async_session_provider: Optional[AsyncSessionProvider] = None):
        """初始化新闻数据源

        Args:
            config: 数据源配置，如果为None则使用默认配置
        """
        if config is None:
            config = DataSourceConfig(
                source_type=DataSourceType.NEWS,
                enabled=True,
                priority=7,  # 新闻数据的优先级
                max_results=20  # 最大返回结果数
            )

        super().__init__(config)

        # 注入异步数据库会话提供者
        # 这里假设通过依赖注入容器获取，实际项目中可能需要从容器中获取
        self.async_session_provider: Optional[AsyncSessionProvider] = async_session_provider

    def set_async_session_provider(self, provider: AsyncSessionProvider) -> None:
        """设置异步会话提供者

        Args:
            provider: 异步会话提供者
        """
        self.async_session_provider = provider
        self.logger.info("已设置异步数据库会话提供者")

    async def _test_internal_connection(self) -> bool:
        """测试数据库连接

        Returns:
            bool: 连接是否成功
        """
        try:
            # 检查会话提供者是否可用
            if not self.async_session_provider:
                self.logger.error("异步会话提供者未设置")
                return False

            # 测试数据库连接 - 执行一个简单的查询
            async with self.async_session_provider() as session:
                # 查询新闻表是否存在并且能够访问
                result = await session.execute(
                    select(func.count(NewsModel.id)).limit(1)
                )
                count = result.scalar()

                self.logger.info(f"数据库连接测试成功，当前新闻数量: {count}")
                return True

        except Exception as e:
            self.logger.error(f"数据库连接测试失败: {str(e)}")
            return False

    def _get_description(self) -> str:
        """获取数据源描述"""
        return "从新闻数据库收集数据库"

    async def _collect_data_impl(
            self,
            query: str,
            # keywords: List[str],
            filters: Optional[Dict[str, Any]] = None
    ) -> List[CollectedData]:
        """从数据库收集新闻数据

        Args:
            query: 查询字符串
            keywords: 关键词列表
            filters: 过滤条件

        Returns:
            List[CollectedData]: 收集到的新闻数据列表
        """
        try:
            if not self.async_session_provider:
                self.logger.error("异步会话提供者未设置")
                return []

            collected_data = []

            async with self.async_session_provider() as session:
                # 构建基础查询
                stmt = select(NewsModel)

                # 应用关键词过滤
                # if keywords:
                #     # 构建关键词搜索条件 - 在标题和内容中搜索关键词
                #     keyword_conditions = []
                #     for keyword in keywords:
                #         keyword_like = f"%{keyword}%"
                #         keyword_conditions.append(
                #             NewsModel.title.ilike(keyword_like) |
                #             NewsModel.content.ilike(keyword_like)
                #         )
                #
                #     # 使用 OR 连接所有关键词条件（匹配任一关键词即可）
                #     if keyword_conditions:
                #         from sqlalchemy import or_
                #         stmt = stmt.where(or_(*keyword_conditions))

                # 应用额外的过滤条件
                if filters:
                    # 按来源过滤
                    if 'source' in filters and filters['source']:
                        stmt = stmt.where(NewsModel.source == filters['source'])

                    # 按发布时间范围过滤
                    if 'start_date' in filters and filters['start_date']:
                        stmt = stmt.where(NewsModel.published_at >= filters['start_date'])

                    if 'end_date' in filters and filters['end_date']:
                        stmt = stmt.where(NewsModel.published_at <= filters['end_date'])

                # 按发布时间降序排列（最新的在前）
                stmt = stmt.order_by(desc(NewsModel.published_at))

                # 限制结果数量
                max_results = self.config.max_results or 20
                stmt = stmt.limit(max_results)

                # 执行查询
                result = await session.execute(stmt)
                news_items = result.scalars().all()

                self.logger.info(f"从数据库查询到 {len(news_items)} 条新闻")

                # 将查询结果转换为 CollectedData 对象
                for news_item in news_items:
                    # 构建元数据
                    metadata = {
                        'source': news_item.source or 'unknown',
                        'published_at': news_item.published_at.isoformat() if news_item.published_at else None,
                        'news_id': str(news_item.id),
                        'data_source': 'internal_database'
                    }

                    # 生成标签
                    tags = ['news', 'internal']
                    if news_item.source:
                        tags.append(f"source_{news_item.source.lower()}")

                    # 创建收集数据对象
                    data = self._create_collected_data(
                        title=news_item.title,
                        content=news_item.content,
                        metadata=metadata,
                        tags=tags,
                        url=news_item.id
                    )

                    collected_data.append(data)

                # 根据相关性过滤数据(why keywords?)
                # if keywords:
                #     collected_data = self._filter_by_relevance(
                #         collected_data,
                #         keywords,
                #         min_relevance=0.1  # 设置较低的阈值，因为已经通过SQL过滤了
                #     )

                self.logger.info(f"最终返回 {len(collected_data)} 条新闻数据")
                return collected_data

        except Exception as e:
            self.logger.error(f"从数据库收集新闻数据失败: {str(e)}")
            return []

    def _create_collected_data(
            self,
            title: str,
            content: str,
            metadata: Optional[Dict[str, Any]] = None,
            tags: Optional[List[str]] = None,
            url: Optional[str] = None,
    ) -> CollectedData:
        """
        创建收集数据对象

        Args:
            title: 标题
            content: 内容
            metadata: 元数据
            tags: 标签

        Returns:
            CollectedData: 收集的数据对象
        """
        return CollectedData(
            source_type=self.config.source_type,
            source_id=self.data_source.id,
            title=title,
            content=content,
            metadata=metadata or {},
            tags=tags or [],
            url=url
        )

    def _calculate_completeness_score(self, data: CollectedData) -> float:
        """计算新闻数据完整性

        Args:
            data: 收集的数据

        Returns:
            float: 完整性评分 (0.0-1.0)
        """
        score = 0.0

        # 检查标题完整性 (25%)
        if data.title and len(data.title.strip()) > 5:
            score += 0.25

        # 检查内容完整性 (50%)
        if data.content and len(data.content.strip()) > 50:
            score += 0.5
        elif data.content and len(data.content.strip()) > 10:
            score += 0.25  # 内容较短，给部分分数

        # 检查来源信息 (15%)
        if data.metadata.get('source') and data.metadata['source'] != 'unknown':
            score += 0.15

        # 检查发布时间 (10%)
        if data.metadata.get('published_at'):
            score += 0.1

        return min(score, 1.0)  # 确保不超过1.0

    def get_news_summary(self) -> Dict[str, Any]:
        """获取新闻数据源摘要信息

        Returns:
            Dict[str, Any]: 摘要信息
        """
        summary = {
            'source_type': self.config.source_type.value,
            'enabled': self.config.enabled,
            'priority': self.config.priority,
            'max_results': self.config.max_results,
            'has_session_provider': bool(self.async_session_provider),
            'description': '从内部新闻数据库收集新闻数据'
        }

        return summary

    async def get_news_statistics(self) -> Dict[str, Any]:
        """获取新闻数据库统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            if not self.async_session_provider:
                return {'error': '数据库会话提供者未设置'}

            async with self.async_session_provider() as session:
                # 总新闻数量
                total_count_result = await session.execute(select(func.count(NewsModel.id)))
                total_count = total_count_result.scalar()

                # 按来源统计
                source_stats_result = await session.execute(
                    select(NewsModel.source, func.count(NewsModel.id))
                    .group_by(NewsModel.source)
                    .order_by(desc(func.count(NewsModel.id)))
                )
                source_stats = dict(source_stats_result.all())

                # 最近一条新闻的时间
                latest_news_result = await session.execute(
                    select(NewsModel.published_at)
                    .order_by(desc(NewsModel.published_at))
                    .limit(1)
                )
                latest_news_time = latest_news_result.scalar()

                return {
                    'total_count': total_count,
                    'source_distribution': source_stats,
                    'latest_news_time': latest_news_time.isoformat() if latest_news_time else None
                }

        except Exception as e:
            self.logger.error(f"获取新闻统计信息失败: {str(e)}")
            return {'error': str(e)}
