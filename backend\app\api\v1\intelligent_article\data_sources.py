#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能文章生成 - 数据源管理API
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone
from tornado.web import HTTPError
from pydantic import BaseModel, Field
from dependency_injector.wiring import Provide, inject

from app.api.base import BaseHandler
from app.core.middleware import auth_middleware
from app.core.config import settings
from app.core.di.containers import Container
from app.services.reasoning.intelligent_article_generator.data_sources.data_source_manager import DataSourceManager
from app.services.reasoning.intelligent_article_generator.models.data_source_models import DataSourceType
from app.utils.data_source_http_client import DataSourceHTTPClient
from app.services.reference.reference_manager import ReferenceManager
from app.services.reference.reference_models import ReferenceDoc

logger = logging.getLogger(__name__)


class DataSourceQueryRequest(BaseModel):
    """数据源查询请求模型"""
    query: str = Field(description="查询关键词")
    source_types: Optional[List[str]] = Field(default=None, description="数据源类型列表")
    limit: Optional[int] = Field(default=10, description="返回结果数量限制")
    filters: Optional[Dict[str, Any]] = Field(default_factory=dict, description="额外过滤条件")


class DataSourceListHandler(BaseHandler):
    """数据源列表处理器"""

    @inject
    def initialize(
        self,
        data_source_manager: DataSourceManager = Provide[Container.data_source_manager]
    ):
        """初始化处理器"""
        super().initialize()
        self.data_source_manager: DataSourceManager = data_source_manager
        self.data_source_client = DataSourceHTTPClient()
        
    async def get(self):
        """列出所有可用的数据源"""
        try:

            # if settings.data_source_proxy.ENABLE_DATA_SOURCE_PROXY:
            #     logger.info("启用数据源代理模式，调用164环境接口")
            #     # 获取查询参数
            #     params = {}
            #     for key, values in self.request.arguments.items():
            #         if values:
            #             params[key] = values[0].decode() if isinstance(values[0], bytes) else values[0]
            #
            #     # 调用164环境接口
            #     result = await self.data_source_client.call_internal_api(
            #         method="GET",
            #         endpoint="/api/v1/article/data-sources/list",
            #         params=params
            #     )
            #
            #     if "error" not in result:
            #         # no success response
            #         self.finish(result)
            #         return
            #     else:
            #         logger.warning(f"调用164环境接口失败: {result['error']}，回退到本地处理")
            #         self.write_error(500, error_message="获取数据源请求错误")
            #         return

            # 使用 data_sources 属性获取数据源字典
            data_sources = self.data_source_manager.data_sources

            source_info = []
            for source_type, source_instance in data_sources.items():
                info = {
                    "name": source_instance.data_source.name,
                    "type": source_instance.data_source.source_type.value,
                    "description": source_instance.data_source.description,
                    "is_available": source_instance.is_healthy(),
                    "rate_limit": source_instance.config.rate_limit
                }
                source_info.append(info)

            self.success_response({
                "data_sources": source_info,
                "total_count": len(source_info)
            }, "数据源列表获取成功")

        except Exception as e:
            logger.error(f"获取数据源列表失败: {str(e)}")
            raise HTTPError(500, f"获取数据源列表失败: {str(e)}")


class DataSourceStatusHandler(BaseHandler):
    """数据源状态处理器"""

    @inject
    def initialize(
        self,
        data_source_manager: DataSourceManager = Provide[Container.data_source_manager]

    ):
        """初始化处理器"""
        super().initialize()
        self.data_source_manager: DataSourceManager = data_source_manager,
        
    async def get(self):
        """获取数据源状态"""
        try:
            source_name = self.get_argument("source", None)
            
            if source_name:
                # 获取特定数据源状态
                # 查找匹配的数据源类型
                target_source_type = None
                for source_type in self.data_source_manager.data_sources.keys():
                    if source_type.value == source_name:
                        target_source_type = source_type
                        break

                if not target_source_type or target_source_type not in self.data_source_manager.data_sources:
                    raise HTTPError(404, f"数据源 '{source_name}' 不存在")

                source = self.data_source_manager.data_sources[target_source_type]
                source_data = source.get_status()

                status = {
                    "name": source_name,
                    "is_available": source.is_healthy(),
                    "status": source_data.status.value,
                    "total_requests": source_data.total_requests,
                    "success_rate": source_data.get_success_rate(),
                    "last_success_at": source_data.last_success_at,
                    "last_error": source_data.last_error,
                    "total_data_collected": source_data.total_data_collected
                }

                self.success_response(status, f"数据源 '{source_name}' 状态获取成功")
            else:
                # 获取所有数据源状态
                status_dict = self.data_source_manager.get_source_status()
                status_list = []

                for source_type, status_info in status_dict.items():
                    status_list.append({
                        "name": source_type.value,
                        **status_info
                    })

                self.success_response({
                    "sources_status": status_list,
                    "total_count": len(status_list)
                }, "所有数据源状态获取成功")
                
        except HTTPError:
            raise
        except Exception as e:
            logger.error(f"获取数据源状态失败: {str(e)}")
            raise HTTPError(500, f"获取数据源状态失败: {str(e)}")


@auth_middleware(required=True)
class DataSourceQueryHandler(BaseHandler):
    """数据源查询处理器"""

    @inject
    def initialize(
        self,
        data_source_manager: DataSourceManager = Provide[Container.data_source_manager],
        reference_manager: ReferenceManager = Provide[Container.reference_manager]
    ):
        """初始化处理器"""
        super().initialize()
        self.data_source_manager: DataSourceManager = data_source_manager
        self.reference_manager: ReferenceManager = reference_manager
        self.data_source_client = DataSourceHTTPClient()
        
    async def post(self):
        """查询数据源（复杂查询）"""
        try:
            if not self.json_body:
                raise HTTPError(400, "缺少请求数据")

            # 检查是否启用数据源代理
            # if settings.data_source_proxy.ENABLE_DATA_SOURCE_PROXY:
            #     logger.info("启用数据源代理模式，调用164环境接口")
            #
            #     # 调用164环境接口
            #     result = await self.data_source_client.call_internal_api(
            #         method="POST",
            #         endpoint="/api/v1/article/data-sources/query",
            #         data=self.json_body
            #     )
            #
            #     if "error" not in result:
            #         # 成功获取数据，同步写入当前引用管理
            #         try:
            #             current_uid = self.current_user.id if (self.current_user and self.current_user.id) else None
            #             if current_uid:
            #                 try:
            #                     await self.reference_manager.clear_by_owner(current_uid)
            #                 except Exception:
            #                     logger.warning("清空用户历史引用失败，继续尝试创建新引用", exc_info=True)
            #
            #                 now = datetime.now(timezone.utc)
            #                 docs: List[ReferenceDoc] = []
            #                 # 兼容 results_by_source 结构（与本地实现一致）
            #                 try:
            #                     data = result.get("data") or {}
            #                     results_by_source = data.get("results_by_source") or {}
            #                     for source_name, items in results_by_source.items():
            #                         if not isinstance(items, list):
            #                             continue
            #                         for it in items:
            #                             try:
            #                                 # it 为 CollectedData 的序列化结果
            #                                 title = it.get("title") or self.json_body.get("query")
            #                                 url = it.get("url")
            #                                 content_excerpt = it.get("content")
            #                                 tags = list({source_name, *([t for t in (it.get("tags") or [])])})
            #                                 metadata = it.get("metadata") or {}
            #                                 payload = it
            #                                 docs.append(
            #                                     ReferenceDoc(
            #                                         id=it.get("id"),
            #                                         type="data_source",
            #                                         title=title,
            #                                         source=source_name,
            #                                         url=url,
            #                                         content_excerpt=content_excerpt,
            #                                         payload=payload,
            #                                         tags=tags,
            #                                         visibility="private",
            #                                         owner_id=current_uid,
            #                                         created_at=now,
            #                                         updated_at=now,
            #                                         metadata=metadata,
            #                                         fingerprint=None,
            #                                     )
            #                                 )
            #                             except Exception:
            #                                 logger.warning("代理结果构建引用文档失败，已跳过某条结果", exc_info=True)
            #
            #                     if docs:
            #                         try:
            #                             await self.reference_manager.batch_create(docs)
            #                         except Exception:
            #                             logger.error("代理批量创建引用失败", exc_info=True)
            #                 except Exception:
            #                     logger.warning("解析代理结果失败，跳过引用写入", exc_info=True)
            #
            #         finally:
            #             # 无论是否写入成功，都返回代理结果
            #             self.finish(result)
            #             return
            #     else:
            #         logger.warning(f"调用164环境接口失败: {result['error']}，回退到本地处理")
            #         raise HTTPError(500, f"调用164环境接口失败: {str(result['error'])}")

            request = DataSourceQueryRequest(**self.json_body)
            
            logger.info(f"执行数据源查询: {request.query}")
            
            # 执行查询
            # 将字符串转换为 DataSourceType 枚举
            source_types = None
            if request.source_types:
                source_types = []
                for st in request.source_types:
                    # 直接通过枚举值查找
                    for source_type in DataSourceType:
                        if source_type.value == st.lower():
                            source_types.append(source_type)
                            break

            results, _ = await self.data_source_manager.collect_all_data(
                query=request.query,
                filters={**request.filters, "limit": request.limit},
                source_types=source_types
            )

            # 将结果写入引用管理：先清空当前用户历史，再创建新引用
            current_uid = self.current_user.id if (self.current_user and self.current_user.id) else None
            if current_uid:
                try:
                    await self.reference_manager.clear_by_owner(current_uid)
                except Exception:
                    logger.warning("清空用户历史引用失败，继续尝试创建新引用", exc_info=True)

                now = datetime.now(timezone.utc)
                docs: List[ReferenceDoc] = []
                for r in results:
                    try:
                        source_name = r.source_type.value
                        payload = r.model_dump()
                        # 合并来源标签与数据源自带标签，并去重
                        merged_tags = list({source_name, *(r.tags or [])})
                        docs.append(
                            ReferenceDoc(
                                id=r.id,
                                type="data_source",
                                title=r.title,
                                source=source_name,
                                url=r.url,
                                content_excerpt=r.content,
                                payload=payload,
                                tags=merged_tags,
                                visibility="private",
                                owner_id=current_uid,
                                created_at=now,
                                updated_at=now,
                                metadata=(r.metadata or {}),
                                fingerprint=None,
                            )
                        )
                    except Exception:
                        logger.warning("构建引用文档失败，已跳过某条结果", exc_info=True)
                if docs:
                    try:
                        await self.reference_manager.batch_create(docs)
                    except Exception:
                        logger.error("批量创建引用失败", exc_info=True)

            # 按数据源分组结果
            grouped_results = {}
            for result in results:
                source_name = result.source_type.value
                if source_name not in grouped_results:
                    grouped_results[source_name] = []
                grouped_results[source_name].append(result.model_dump())
                
            response_data = {
                "query": request.query,
                "results_by_source": grouped_results,
                "total_results": len(results),
                "sources_queried": len(grouped_results)
            }
            
            self.success_response(response_data, "数据源查询成功")
            
        except Exception as e:
            logger.error(f"查询数据源失败: {str(e)}")
            raise HTTPError(500, f"查询数据源失败: {str(e)}")




