"""add news table

Revision ID: d64fbbb0aac1
Revises: 633b25e6d23a
Create Date: 2025-08-26 17:49:19.723585

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa

from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'd64fbbb0aac1'
down_revision: Union[str, None] = '633b25e6d23a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # 创建简化的新闻表
    op.create_table('history_news',
                    sa.Column('id', sa.VARCHAR(length=64), autoincrement=False, nullable=False, comment='主键ID'),
                    sa.Column('title', sa.VARCHAR(length=255), autoincrement=False, nullable=False, comment='新闻标题'),
                    sa.Column('content', sa.TEXT(), autoincrement=False, nullable=False, comment='新闻内容'),
                    sa.Column('source', sa.VARCHAR(length=100), autoincrement=False, nullable=True, comment='新闻来源'),
                    sa.Column('published_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=True,
                              comment='发布时间'),
                    sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False,
                              comment='创建时间'),
                    sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), autoincrement=False, nullable=False,
                              comment='更新时间'),
                    sa.Column('created_by', sa.VARCHAR(length=64), autoincrement=False, nullable=True,
                              comment='创建人ID'),
                    sa.Column('updated_by', sa.VARCHAR(length=64), autoincrement=False, nullable=True,
                              comment='更新人ID'),
                    sa.Column('version', sa.VARCHAR(length=64), autoincrement=False, nullable=True, comment='版本号'),
                    sa.PrimaryKeyConstraint('id', name='news_pkey'),
                    comment='新闻表'
                    )


def downgrade() -> None:
    """Downgrade schema."""
    op.drop_table('history_news')