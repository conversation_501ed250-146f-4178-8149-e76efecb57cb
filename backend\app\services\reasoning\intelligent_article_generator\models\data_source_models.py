#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据源相关模型

定义数据源类型、配置、收集的数据等模型
"""

import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Union
from enum import Enum
from pydantic import BaseModel, Field, field_validator

ZHIHU_COOKIES="_xsrf=ClCzydA5cFu9m9MnfE47X55Y8csMQgge; __zse_ck=004_/E5ssDjDJYpKRSadPn96RFqNkEILq0opqgCd1vLZjjX7pXTDS6QTzNECr8D0XAsaynivjEGwA9EAh6QJQk6lC9xKiHdKJcLsQq52WXR=YWA7ZtECN8=UFRiK55NNgugQ-9upplYAfeW7crl0dWvGDDyw40Tfeko55fvfaiKYWy92GehiQemWNbqLPg3ADV1JouIugFeTWLgrr1QojuLC8dGrbb5HekDqlB2Mm483faudyNktdj4xcnPN7pT2hA0oP; _zap=f3709cb6-e2a1-4733-a8af-2cbb56a8d2a4; BEC=b7b0f394f3fd074c6bdd2ebbdd598b4e; d_c0=__bTqjAk9BqPTkzK1i0I6lfF-hAa3IRumXI=|**********; Hm_lvt_98beee57fd2ef70ccdd5ca52b9740c49=**********,**********; HMACCOUNT=E6125208E05D76CE; DATE=*************; cmci9xde=U2FsdGVkX19sHN8AntsY1r1mZ9ZbZ8SWInDcTqswFZzsoVqxTLA/Of1AbyGML5Bal9bivWBbu6AwsoTm2oeWWw==; pmck9xge=U2FsdGVkX19jbjeWz/mgHtxeOBRAeEgHd+CKmVRB9FQ=; crystal=U2FsdGVkX19ic7TDSr5iDiz1xDJmaYx22JkhnqiSZ88L79SSEVozlMunJCxoKwjywuSs8UWzM5hLozkYLUza3IQQwuC+JkDN9r7fRG7svVbFp/WoQReISGV1xVOVs4ikrLY8aSAd20AVbGW3vTK267JqvkxnSC7ls5qyiOsA3vEMV47gq2ihaOn29eet58/8FYz4P1ND5BVkFZhQM2kvvjeNu62xKLAeWU5384MaNhD8G56YKrGqKcCuGgAQ/XfW; assva6=U2FsdGVkX19xHP8rpO+tqyorBd/zL7VlJg7cdisF+7g=; assva5=U2FsdGVkX1+4+BAeVAK8FK31zU6flO6f4Qwe/q8d8dgrLch2L4VwsUEtyD+lYKONJ2THUBAKtoryoEWYpJmc2Q==; __snaker__id=Bu5zQiQbQafaehb7; vmce9xdq=U2FsdGVkX19mfrNDus+WgyHrJqCwJOkvvDK23Ln/1dO0Ud2xrPNLXQaRljduM0M+SE3DpU5wFKzMOrJmw5PR4iT/Pik7fPkbsMBNzRbrBmQkm3imqzbP90FXw/W1yVblq09bho7wg++SOaOhOjKCxWZoT1hjcQYAMYn+11nzJA8=; gdxidpyhxdE=yaJd54pupm0LKRahoxf%2B7GZ9e81H7LJcsCIVWdOvRnbhq9cmsBLflLfoKcN69AKdrHUV%5CIcIeA%2FED0Zm0IVqj1f%2Bnl9usr1mJLqGEBCJdm9LNnglB%5C1bUDm8oUtdCoMny0GHj9Gf7qwl4VAx3t%2BiHt%5CHW8oAXk%5CS8bJZQlzrzmCbNslE%3A1755841550327; captcha_session_v2=2|1:0|10:1755840696|18:captcha_session_v2|88:a2V0TGFveXlaSVVSWTBBOWNubzhtKytQMnVCTE5jcjlONmZQdjhLMFdMSUZJMjF3ajgva1I5RDArcThFdnVzVg==|40da92ead75f7867959cd38137f40dfcf3a71acdf69477e75202f833586ea997; captcha_ticket_v2=2|1:0|10:1755840711|17:captcha_ticket_v2|728:eyJ2YWxpZGF0ZSI6IkNOMzFfVFJHZVlvWXdUNmIqc0ZoSElLX1YwU21sLkRrT2l0XzZ2U0FNRDZvZGFkUmZmd1VMU3oxTlh3STg1RXNqTHhIZExuNHhGNm1uQnU5U2FXbnkuNG5RUktjUVI5dVdwZVFUaWVkX0NNV1l2VVFpeFRfVWdha1Fua19ZREJGeVlpbUFCczlJU2pFVlgyNXZkQXouKlREQllvNkoud0E2UXlQbEM4WnU4cWNvdVhoeFdfaTMxSlNIQVB5bmxjbypSczhzeFpBZEc5NHFBVi5sZENHTlRRSC5kOGFGT21TRDMxWSpHNHJ4UFRkKjEqdUsyUzZzOVNKTkNSOXhFNnZDLlUyNmF4X1NhZmliNUJQendqYip3TkphVV81RHdLUWVWM2IuV3JWSVc1V1pOMVMzTVZRY21OeDVIbnc4bVpseip0VXZBSzRmM2lqRG5PSE56UFJGbmZqNHkxaXZ0R3o1YU1nWmc1b1duZlE4VmdjdFduLndTQ01SX2J0TVRzY3o4RXhEUVZRWGlKbjFWWDNjSypIZGhHV25ReWoqXzFCT2J1WDh5a0xvRlVfY0pBNUpoWWs1ek02MklJOWtid2FNODNrOUEyX0ZXMGFuT1l2aXpBTkppNmRtMGozMzRUYWpTVlNxQzZzRGtDZkNQczJCNmRwTF9HRWZzeXZSSmFtM0NDbzZwdkRZZVk3N192X2lfMSJ9|553154d51e5d43952a6fc816e78de36122ae1e0c4bb46829fcec41711c16a0a5; z_c0=2|1:0|10:1755840712|4:z_c0|92:Mi4xa3lOREFnQUFBQURfOXRPcU1DVDBHaVlBQUFCZ0FsVk55RTZWYVFEMWxyUFNPUEU4NUNFV1Z4eUd1ZUhzR01UdVJ3|db038f25c1bd65a64101d2f182b1c05c6907347a2e85fc14426acf39b39a38ac; Hm_lpvt_98beee57fd2ef70ccdd5ca52b9740c49=1755840713"

class DataSourceType(str, Enum):
    """数据源类型"""
    # 内部数据源
    README = "readme"
    ANALYSIS = "analysis"
    PROJECT = "project"
    INTERNAL_DB = "internal_db"
    CODE = "code"
    NEWS = "history_news_database"

    # 外部数据源
    STACKOVERFLOW = "stackoverflow"
    REDDIT = "reddit"
    HACKERNEWS = "hackernews"
    DEVTO = "devto"
    PRODUCTHUNT = "producthunt"
    GITHUB = "github"
    MEDIUM = "medium"
    TWITTER = "twitter"
    ARXIV = "arxiv"
    YOUTUBE = "youtube"
    TECHBLOG = "techblog"
    TECHNEWS = "technews"

    # 国内数据源
    JUEJIN = "juejin"           # 掘金
    CSDN = "csdn"               # CSDN
    CNBLOGS = "cnblogs"         # 博客园
    OSCHINA = "oschina"         # 开源中国
    SEGMENTFAULT = "segmentfault"  # SegmentFault
    ZHIHU = "zhihu"             # 知乎
    WEIBO = "weibo"             # 微博
    BILIBILI = "bilibili"       # 哔哩哔哩
    DOUYIN = "douyin"           # 抖音
    XIAOHONGSHU = "xiaohongshu" # 小红书


class DataSourceStatus(str, Enum):
    """数据源状态"""
    INACTIVE = "inactive"
    ACTIVE = "active"
    ERROR = "error"
    RATE_LIMITED = "rate_limited"
    MAINTENANCE = "maintenance"


class DataQuality(BaseModel):
    """数据质量评估"""
    relevance_score: float = Field(ge=0.0, le=1.0, description="相关性评分")
    freshness_score: float = Field(ge=0.0, le=1.0, description="新鲜度评分")
    authority_score: float = Field(ge=0.0, le=1.0, description="权威性评分")
    completeness_score: float = Field(ge=0.0, le=1.0, description="完整性评分")
    overall_score: float = Field(default=0.0, ge=0.0, le=1.0, description="总体质量评分")
    
    def calculate_overall_score(self) -> float:
        """计算总体质量评分"""
        weights = {
            'relevance': 0.4,
            'freshness': 0.2,
            'authority': 0.2,
            'completeness': 0.2
        }
        
        self.overall_score = (
            self.relevance_score * weights['relevance'] +
            self.freshness_score * weights['freshness'] +
            self.authority_score * weights['authority'] +
            self.completeness_score * weights['completeness']
        )
        return self.overall_score


class CollectedData(BaseModel):
    """收集的数据"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    source_type: DataSourceType = Field(description="数据源类型")
    source_id: str = Field(description="数据源标识")
    title: str = Field(description="数据标题")
    content: str = Field(description="数据内容")
    url: Optional[str] = Field(None, description="数据URL")
    author: Optional[str] = Field(None, description="作者")
    published_at: Optional[datetime] = Field(None, description="发布时间")
    tags: List[str] = Field(default_factory=list, description="标签")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="元数据")
    quality: Optional[DataQuality] = Field(None, description="数据质量评估")
    collected_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    @field_validator("content")
    def content_not_empty(cls, v):
        if not v or v.strip() == "":
            raise ValueError("数据内容不能为空")
        return v


class DataSourceConfig(BaseModel):
    """数据源配置"""
    source_type: DataSourceType = Field(description="数据源类型")
    enabled: bool = Field(default=True, description="是否启用")
    priority: int = Field(default=1, description="优先级(1-10)")
    rate_limit: int = Field(default=100, description="速率限制(请求/小时)")
    timeout: int = Field(default=300, description="超时时间(秒)")
    max_results: int = Field(default=50, description="最大结果数")
    supports_hot_rank: bool = Field(default=False, description="是否支持热点排行数据")
    
    # API配置
    api_key: Optional[str] = Field(None, description="API密钥")
    api_secret: Optional[str] = Field(None, description="API密钥（用于OAuth等）")
    api_url: Optional[str] = Field(None, description="API地址")
    api_version: Optional[str] = Field(None, description="API版本")
    
    # 搜索配置
    search_keywords: List[str] = Field(default_factory=list, description="搜索关键词")
    search_filters: Dict[str, Any] = Field(default_factory=dict, description="搜索过滤器")
    
    # 质量控制
    min_quality_score: float = Field(default=0.5, description="最小质量评分")
    content_filters: List[str] = Field(default_factory=list, description="内容过滤器")
    
    # 缓存配置
    cache_enabled: bool = Field(default=True, description="是否启用缓存")
    cache_ttl: int = Field(default=3600, description="缓存TTL(秒)")
    
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))


class DataSource(BaseModel):
    """数据源基础模型"""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str = Field(description="数据源名称")
    source_type: DataSourceType = Field(description="数据源类型")
    description: str = Field(description="数据源描述")
    config: DataSourceConfig = Field(description="数据源配置")
    status: DataSourceStatus = Field(default=DataSourceStatus.INACTIVE, description="数据源状态")
    
    # 统计信息
    total_requests: int = Field(default=0, description="总请求数")
    successful_requests: int = Field(default=0, description="成功请求数")
    failed_requests: int = Field(default=0, description="失败请求数")
    last_request_at: Optional[datetime] = Field(None, description="最后请求时间")
    last_success_at: Optional[datetime] = Field(None, description="最后成功时间")
    last_error: Optional[str] = Field(None, description="最后错误信息")
    
    # 数据统计
    total_data_collected: int = Field(default=0, description="总收集数据量")
    average_quality_score: float = Field(default=0.0, description="平均质量评分")
    
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
    def update_statistics(self, success: bool, error: Optional[str] = None) -> None:
        """更新统计信息"""
        self.total_requests += 1
        self.last_request_at = datetime.now(timezone.utc)
        
        if success:
            self.successful_requests += 1
            self.last_success_at = datetime.now(timezone.utc)
            self.last_error = None
        else:
            self.failed_requests += 1
            self.last_error = error
        
        self.updated_at = datetime.now(timezone.utc)
    
    def get_success_rate(self) -> float:
        """获取成功率"""
        if self.total_requests == 0:
            return 0.0
        return self.successful_requests / self.total_requests
    
    def is_healthy(self) -> bool:
        """检查数据源是否健康"""
        if self.status != DataSourceStatus.ACTIVE:
            return False

        # 如果没有请求历史，但状态是ACTIVE，认为是健康的（新初始化的数据源）
        if self.total_requests == 0:
            return True

        # 如果有请求历史，检查成功率
        success_rate = self.get_success_rate()

        # 如果请求次数很少（<5次），降低成功率要求
        if self.total_requests < 5:
            return success_rate >= 0.6  # 60%的成功率

        # 正常情况下要求80%的成功率
        return success_rate >= 0.8


class DataSourceMetrics(BaseModel):
    """数据源指标"""
    source_id: str = Field(description="数据源ID")
    collection_time: datetime = Field(description="收集时间")
    data_count: int = Field(description="数据数量")
    average_quality: float = Field(description="平均质量")
    processing_time: float = Field(description="处理时间(秒)")
    errors: List[str] = Field(default_factory=list, description="错误列表")
    warnings: List[str] = Field(default_factory=list, description="警告列表")
