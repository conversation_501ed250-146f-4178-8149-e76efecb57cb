#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
# <AUTHOR> wangzc
# @Date    : 2025/7/31 13:06
# @File    : wechat_article.py
# @Description:
"""
import uuid
import re

import structlog
from bs4 import BeautifulSoup
from tornado.web import HTTPError
from dependency_injector.wiring import inject, Provide

from app.api.base import BaseHandler
from app.core.config import settings
from app.core.di.containers import Container
from app.services.article.article_service import ArticleService
from app.utils.data_source_http_client import DataSourceHTTPClient
from app.utils.web_content_extractor import WebContentExtractor
from app.utils.wechat_articles_util import WechatArticlesUtil
from app.core.middleware import auth_middleware, require_auth

logger = structlog.get_logger(__name__)


class ProxyShowHandler(BaseHandler):
    """微信公众号文章统计处理器"""

    @inject
    def initialize(
            self,
            article_service: ArticleService = Provide[Container.article_service]
    ):
        """初始化处理器

        Args:
            article_service: 文章服务
        """
        self.article_service = article_service
        super().initialize()

    def _is_valid_uuid(self, uuid_string: str) -> bool:
        """
        检查字符串是否为有效的UUID格式
        
        Args:
            uuid_string (str): 要检查的字符串
            
        Returns:
            bool: 如果是有效UUID返回True，否则返回False
        """
        try:
            # 尝试解析UUID
            uuid_obj = uuid.UUID(uuid_string)
            return True
        except ValueError:
            return False
    
    def _is_valid_url(self, url_string: str) -> bool:
        """
        检查字符串是否为有效的URL格式
        
        Args:
            url_string (str): 要检查的字符串
            
        Returns:
            bool: 如果是有效URL返回True，否则返回False
        """
        # 简单的URL验证正则表达式
        url_pattern = re.compile(
            r'^https?://'  # http:// 或 https://
            r'(?:(?:[A-Z0-9](?:[A-Z0-9-]{0,61}[A-Z0-9])?\.)+[A-Z]{2,6}\.?|'  # 域名
            r'localhost|'  # localhost
            r'\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'  # IP地址
            r'(?::\d+)?'  # 可选的端口号
            r'(?:/?|[/?]\S+)$', re.IGNORECASE)
        
        return bool(url_pattern.match(url_string))

    # @require_auth
    async def get(self):
        """获取文章连接的html字符串信息"""
        try:
            # 获取查询参数
            link = self.get_argument("link", None)
            logger.info("获取文章连接", link=link)

            if not link:
                raise HTTPError(400, "缺少link参数")

            # 首先检查是否为有效的UUID
            if self._is_valid_uuid(link):
                logger.info("检测到UUID格式，使用本地数据库查询")
                await self._handle_uuid_request(link)
                return
            
            # 检查是否为有效的URL
            elif self._is_valid_url(link):
                logger.info("检测到URL格式，使用内容提取器处理")
                await self._handle_url_request(link)
                return
            
            # 如果既不是UUID也不是URL，尝试格式化UUID
            else:
                logger.info("尝试格式化UUID字符串")
                formatted_uuid = self._format_uuid_string(link)
                if formatted_uuid and self._is_valid_uuid(formatted_uuid):
                    logger.info("UUID格式化成功，使用本地数据库查询")
                    await self._handle_uuid_request(formatted_uuid)
                    return
                else:
                    raise HTTPError(400, f"无效的link参数格式: {link}")

        except HTTPError:
            # 重新抛出HTTP错误
            raise
        except Exception as e:
            logger.error("获取文章统计信息失败", error=str(e))
            raise HTTPError(500, f"获取统计信息失败: {str(e)}")

    def _format_uuid_string(self, uuid_string: str) -> str:
        """
        尝试将32位十六进制字符串格式化为标准UUID格式
        
        Args:
            uuid_string (str): 32位十六进制字符串
            
        Returns:
            str: 格式化后的UUID字符串，如果格式化失败返回None
        """
        try:
            # 移除所有非十六进制字符
            clean_string = re.sub(r'[^0-9a-fA-F]', '', uuid_string)
            
            # 检查长度是否为32位
            if len(clean_string) != 32:
                return None
            
            # 格式化为标准UUID格式 (8-4-4-4-12)
            formatted = f"{clean_string[:8]}-{clean_string[8:12]}-{clean_string[12:16]}-{clean_string[16:20]}-{clean_string[20:32]}"
            
            return formatted
        except Exception as e:
            logger.error(f"UUID格式化失败: {str(e)}")
            return None

    async def _handle_uuid_request(self, uuid_string: str):
        """
        处理UUID请求，从本地数据库查询新闻记录
        
        Args:
            uuid_string (str): 有效的UUID字符串
        """
        try:
            async with self.article_service.async_session() as session:
                # 导入必要的模块
                from sqlalchemy import select
                from app.models.news.news import NewsModel

                stmt = select(NewsModel).where(NewsModel.id == uuid_string)
                result = await session.execute(stmt)
                news_record = result.scalar_one_or_none()

                if not news_record:
                    logger.error(f"未找到ID为 {uuid_string} 的新闻记录")
                    html_content = self._prepare_html_content("<p>未找到指定的新闻记录</p>", "新闻内容")
                    self.finish(html_content)
                    return

                # 提取新闻信息
                title = news_record.title or "新闻标题"
                content = news_record.content or ""
                source = news_record.source or "未知来源"
                published_at = news_record.published_at

                # 构建HTML内容
                content_parts = []

                # 添加来源信息
                if source:
                    content_parts.append(f"<div class='source'><strong>来源:</strong> {source}</div>")

                # 添加发布时间
                if published_at:
                    content_parts.append(
                        f"<div class='publish-time'><strong>发布时间:</strong> {published_at.strftime('%Y-%m-%d %H:%M:%S')}</div>")

                # 添加新闻内容
                if content:
                    # 清理内容中的HTML标签，确保安全
                    cleaned_content = self._clean_news_content(content)
                    content_parts.append(f"<div class='news-content'>{cleaned_content}</div>")
                else:
                    content_parts.append("<p>新闻内容为空</p>")

                # 组合所有内容
                full_content = "".join(content_parts)
                html_content = self._prepare_html_content(full_content, title)
                self.finish(html_content)

        except ImportError as e:
            logger.error(f"导入模块失败: {str(e)}")
            html_content = self._prepare_html_content("<p>系统模块导入失败</p>", "系统错误")
            self.finish(html_content)
        except Exception as e:
            logger.error(f"从数据库提取新闻内容失败: {str(e)}")
            html_content = self._prepare_html_content("<p>数据库查询失败</p>", "系统错误")
            self.finish(html_content)

    async def _handle_url_request(self, url_string: str):
        """
        处理URL请求，使用内容提取器获取网页内容
        
        Args:
            url_string (str): 有效的URL字符串
        """
        try:
            extractor = WebContentExtractor()
            html_content = extractor.extract_content(url_string)
            self.finish(html_content)
        except Exception as e:
            logger.error(f"内容提取失败: {str(e)}")
            html_content = self._prepare_html_content(f"<p>内容提取失败: {str(e)}</p>", "提取错误")
            self.finish(html_content)

    def _prepare_html_content(self, content, title="文章"):
        """准备完整的HTML内容"""
        if content.strip().startswith('<!DOCTYPE') or content.strip().startswith('<html'):
            return content

        html_template = f"""<!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>{title}</title>
        <style>
            body {{
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
                line-height: 1.6;
                max-width: 900px;
                margin: 0 auto;
                padding: 20px;
                color: #333;
                background-color: #fff;
            }}
            h1, h2, h3, h4, h5, h6 {{
                color: #2c3e50;
                margin-top: 1.5em;
                margin-bottom: 0.5em;
            }}
            h1 {{ font-size: 2em; }}
            h2 {{ font-size: 1.5em; }}
            h3 {{ font-size: 1.2em; }}
            p {{ margin-bottom: 1em; }}
            code {{
                background-color: #f1f2f6;
                padding: 2px 6px;
                border-radius: 4px;
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                font-size: 0.9em;
                color: #e74c3c;
            }}
            pre {{
                background-color: #2d3748;
                color: #e2e8f0;
                padding: 16px;
                border-radius: 8px;
                overflow-x: auto;
                margin: 1em 0;
            }}
            pre code {{
                background: none;
                color: inherit;
                padding: 0;
                font-size: 0.9em;
            }}
            blockquote {{
                border-left: 4px solid #3498db;
                padding-left: 16px;
                margin: 1em 0;
                color: #7f8c8d;
                font-style: italic;
            }}
            img {{
                max-width: 100%;
                height: auto;
                border-radius: 4px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }}
            table {{
                border-collapse: collapse;
                width: 100%;
                margin: 1em 0;
            }}
            th, td {{
                border: 1px solid #ddd;
                padding: 8px 12px;
                text-align: left;
            }}
            th {{
                background-color: #f8f9fa;
                font-weight: bold;
            }}
            a {{
                color: #3498db;
                text-decoration: none;
            }}
            a:hover {{
                text-decoration: underline;
            }}
            ul, ol {{ padding-left: 2em; }}
            li {{ margin-bottom: 0.5em; }}
        </style>
    </head>
    <body>
        <h1>{title}</h1>
        {content}
    </body>
    </html>"""
        return html_template

    def _clean_news_content(self, content: str) -> str:
        """
        清理新闻内容，确保HTML安全

        Args:
            content (str): 原始新闻内容

        Returns:
            str: 清理后的安全HTML内容
        """
        try:
            # 使用 BeautifulSoup 清理内容

            soup = BeautifulSoup(content, 'html.parser')

            # 移除潜在的恶意脚本
            for script in soup(["script", "style", "iframe", "object", "embed"]):
                script.decompose()

            # 移除事件处理器属性
            for tag in soup.find_all():
                # 移除所有以 on 开头的事件属性
                attrs_to_remove = []
                for attr in tag.attrs:
                    if attr.startswith('on') or attr.startswith('javascript:'):
                        attrs_to_remove.append(attr)

                for attr in attrs_to_remove:
                    del tag[attr]

            # 只保留安全的HTML标签
            allowed_tags = [
                'p', 'div', 'span', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
                'ul', 'ol', 'li', 'strong', 'em', 'b', 'i', 'u', 'br', 'hr',
                'blockquote', 'pre', 'code', 'a', 'img', 'table', 'tr', 'td', 'th'
            ]

            # 移除不在允许列表中的标签，但保留其内容
            for tag in soup.find_all():
                if tag.name not in allowed_tags:
                    # 保留标签内容，但移除标签本身
                    tag.unwrap()

            # 清理链接，确保安全性
            for link in soup.find_all('a'):
                if link.get('href'):
                    href = link['href']
                    # 只允许 http 和 https 链接
                    if not href.startswith(('http://', 'https://')):
                        link['href'] = '#'
                        link['title'] = '链接已禁用'

            # 清理图片，确保安全性
            for img in soup.find_all('img'):
                src = img.get('src')
                if src:
                    # 只允许 http 和 https 图片
                    if not src.startswith(('http://', 'https://')):
                        img.decompose()
                    else:
                        # 添加安全属性
                        img['loading'] = 'lazy'
                        img['referrerpolicy'] = 'no-referrer'

            return str(soup)

        except Exception as e:
            logger.error(f"清理新闻内容失败: {str(e)}")
            # 如果清理失败，返回纯文本内容
            return f"<p>{content}</p>"