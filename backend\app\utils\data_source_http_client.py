#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据源HTTP客户端 - 用于调用164环境接口
"""

import aiohttp
import logging
from typing import Dict, Any, Optional
from app.core.config import settings
from aiohttp_socks import ProxyConnector

logger = logging.getLogger(__name__)


class DataSourceHTTPClient:
    """数据源HTTP客户端"""

    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        # 固定代理地址（如需调整，请修改此处）
        if settings.data_source_proxy.ENABLE_DATA_SOURCE_PROXY:
            self._proxy: Optional[str] = "http://192.168.201.164:7890"
        else:
            self._proxy: Optional[str] = None

    async def _get_session(self) -> aiohttp.ClientSession:
        """获取或创建HTTP会话"""
        if self.session is None or self.session.closed:
            connector = ProxyConnector.from_url(self._proxy) if self._proxy else None
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=settings.data_source_proxy.PROXY_TIMEOUT),
                headers={
                    "User-Agent": "GuguApex-DataSource-Client/1.0",
                    "Content-Type": "application/json"
                },
                connector=connector,
            )
        return self.session

    async def call_internal_api(
            self,
            method: str,
            endpoint: str,
            data: Optional[Dict[str, Any]] = None,
            params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        调用164环境API

        Args:
            method: HTTP方法
            endpoint: API端点路径
            data: 请求体数据
            params: 查询参数

        Returns:
            响应数据
        """
        try:
            # 构建完整URL
            base_url = settings.data_source_proxy.internal_base_url
            full_url = f"{base_url}{endpoint}"

            session = await self._get_session()

            request_kwargs = {
                "method": method,
                "url": full_url,
                "params": params
            }

            if data and method.upper() in ["POST", "PUT", "PATCH"]:
                request_kwargs["json"] = data

            logger.info(f"调用164环境API: {method} {full_url}")

            async with session.request(**request_kwargs) as response:
                response_text = await response.text()

                if response.status == 200:
                    try:
                        return await response.json()
                    except Exception as e:
                        logger.error(f"解析响应失败: {str(e)}")
                        return {"error": "响应解析失败", "raw_response": response_text}
                else:
                    logger.error(f"API调用失败: {response.status}")
                    return {
                        "error": f"API调用失败: {response.status}",
                        "status_code": response.status,
                        "response": response_text
                    }

        except Exception as e:
            logger.error(f"调用164环境API异常: {str(e)}")
            return {"error": f"调用异常: {str(e)}"}

    async def close(self):
        """关闭HTTP会话"""
        if self.session and not self.session.closed:
            await self.session.close()


# 全局客户端实例