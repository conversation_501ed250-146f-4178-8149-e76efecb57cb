#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内部数据源基类

为所有内部数据源提供通用功能和接口
"""

import logging
from abc import abstractmethod
from typing import Any, Dict, List, Optional

from ..base_data_source import BaseDataSource
from ...models.data_source_models import DataSourceConfig, CollectedData, DataQuality

logger = logging.getLogger(__name__)


class InternalDataSource(BaseDataSource):
    """内部数据源基类"""
    
    def __init__(self, config: DataSourceConfig):
        """
        初始化内部数据源
        
        Args:
            config: 数据源配置
        """
        super().__init__(config)
        self.project_path: Optional[str] = None
        self.project_data: Dict[str, Any] = {}
    
    async def _test_connection(self) -> bool:
        """
        测试内部数据源连接
        内部数据源通常不需要网络连接，主要检查数据可用性
        
        Returns:
            bool: 连接是否成功
        """
        try:
            # 执行具体的连接测试
            return await self._test_internal_connection()
            
        except Exception as e:
            self.logger.error(f"内部数据源连接测试失败: {str(e)}")
            return False
    
    @abstractmethod
    async def _test_internal_connection(self) -> bool:
        """
        测试内部连接的具体实现
        
        Returns:
            bool: 连接是否成功
        """
        pass
    
    def set_project_context(self, project_path: str, project_data: Dict[str, Any]) -> None:
        """
        设置项目上下文
        
        Args:
            project_path: 项目路径
            project_data: 项目数据
        """
        self.project_path = project_path
        self.project_data = project_data
        self.logger.info(f"设置项目上下文: {project_path}")
    
    def _extract_keywords_from_query(self, query: str) -> List[str]:
        """
        从查询中提取关键词
        
        Args:
            query: 查询字符串
            
        Returns:
            List[str]: 关键词列表
        """
        # 简单的关键词提取，实际实现可能需要更复杂的NLP处理
        import re
        
        # 移除标点符号并分割
        words = re.findall(r'\b\w+\b', query.lower())
        
        # 过滤停用词
        stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 
            'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 
            'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
            '的', '了', '在', '是', '有', '和', '或', '但', '与', '为', '从', '到'
        }
        
        keywords = [word for word in words if word not in stop_words and len(word) > 2]
        return keywords
    
    def _calculate_relevance_score(self, content: str, keywords: List[str]) -> float:
        """
        计算内容与关键词的相关性评分
        
        Args:
            content: 内容文本
            keywords: 关键词列表
            
        Returns:
            float: 相关性评分 (0.0-1.0)
        """
        if not keywords or not content:
            return 0.0
        
        content_lower = content.lower()
        matched_keywords = 0
        
        for keyword in keywords:
            if keyword.lower() in content_lower:
                matched_keywords += 1
        
        return matched_keywords / len(keywords)
    
    def _create_collected_data(
        self,
        title: str,
        content: str,
        metadata: Optional[Dict[str, Any]] = None,
        tags: Optional[List[str]] = None
    ) -> CollectedData:
        """
        创建收集数据对象
        
        Args:
            title: 标题
            content: 内容
            metadata: 元数据
            tags: 标签
            
        Returns:
            CollectedData: 收集的数据对象
        """
        return CollectedData(
            source_type=self.config.source_type,
            source_id=self.data_source.id,
            title=title,
            content=content,
            metadata=metadata or {},
            tags=tags or [],
        )
    
    def _filter_by_relevance(
        self, 
        data_list: List[CollectedData], 
        keywords: List[str],
        min_relevance: float = 0.3
    ) -> List[CollectedData]:
        """
        根据相关性过滤数据
        
        Args:
            data_list: 数据列表
            keywords: 关键词列表
            min_relevance: 最小相关性阈值
            
        Returns:
            List[CollectedData]: 过滤后的数据列表
        """
        filtered_data = []
        
        for data in data_list:
            relevance = self._calculate_relevance_score(data.content, keywords)
            if relevance >= min_relevance:
                # 将相关性评分添加到元数据中
                data.metadata['relevance_score'] = relevance
                filtered_data.append(data)
        
        # 按相关性排序
        filtered_data.sort(key=lambda x: x.metadata.get('relevance_score', 0), reverse=True)
        return filtered_data
    
    async def _assess_data_quality(self, data: CollectedData) -> 'DataQuality':
        """
        评估内部数据质量
        内部数据通常具有较高的权威性和完整性
        
        Args:
            data: 数据项
            
        Returns:
            DataQuality: 质量评估结果
        """
        # 内部数据的相关性评分
        relevance_score = data.metadata.get('relevance_score', 0.8)
        
        # 内部数据通常是最新的
        freshness_score = 0.9
        
        # 内部数据具有高权威性
        authority_score = 0.9
        
        # 计算完整性评分
        completeness_score = self._calculate_completeness_score(data)
        
        quality = DataQuality(
            relevance_score=relevance_score,
            freshness_score=freshness_score,
            authority_score=authority_score,
            completeness_score=completeness_score
        )
        
        quality.calculate_overall_score()
        return quality
