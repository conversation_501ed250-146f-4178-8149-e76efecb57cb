#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
外部数据源基类

为所有外部数据源提供通用功能和接口
"""

import asyncio
import aiohttp
from aiohttp_socks import ProxyConnector
import logging
from abc import abstractmethod
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional
from urllib.parse import urlencode

from app.core.config import settings
from ..base_data_source import BaseDataSource
from ...models.data_source_models import DataSourceConfig, CollectedData

logger = logging.getLogger(__name__)


class ExternalDataSource(BaseDataSource):
    """外部数据源基类"""
    
    def __init__(self, config: DataSourceConfig):
        """
        初始化外部数据源
        
        Args:
            config: 数据源配置
        """
        super().__init__(config)
        self.session: Optional[aiohttp.ClientSession] = None
        self.rate_limiter = RateLimiter(config.rate_limit)
        # 固定代理地址（如需调整，请修改此处）
        if settings.data_source_proxy.ENABLE_DATA_SOURCE_PROXY:
            self._proxy: Optional[str] = "http://192.168.201.164:7890"
        else:
            self._proxy: Optional[str] = None
    
    async def initialize(self) -> bool:
        """
        初始化外部数据源
        
        Returns:
            bool: 初始化是否成功
        """
        try:
            # 创建HTTP会话（使用代理连接器）
            timeout = aiohttp.ClientTimeout(total=self.config.timeout)
            connector = ProxyConnector.from_url(self._proxy) if self._proxy else None
            self.session = aiohttp.ClientSession(
                timeout=timeout,
                headers=self._get_default_headers(),
                connector=connector,
            )
            
            # 调用父类初始化
            return await super().initialize()
            
        except Exception as e:
            self.logger.error(f"外部数据源初始化失败: {str(e)}")
            return False
    
    async def _test_connection(self) -> bool:
        """
        测试外部数据源连接
        
        Returns:
            bool: 连接是否成功
        """
        try:
            if not self.session:
                return False
            
            # 执行具体的连接测试
            return await self._test_api_connection()
            
        except Exception as e:
            self.logger.error(f"外部数据源连接测试失败: {str(e)}")
            return False
    
    @abstractmethod
    async def _test_api_connection(self) -> bool:
        """
        测试API连接的具体实现
        
        Returns:
            bool: 连接是否成功
        """
        pass
    
    @abstractmethod
    def _get_api_endpoint(self, query: str, filters: Optional[Dict[str, Any]] = None) -> str:
        """
        获取API端点URL
        
        Args:
            query: 查询字符串
            filters: 过滤条件
            
        Returns:
            str: API端点URL
        """
        pass
    
    @abstractmethod
    async def _parse_api_response(self, response_data: Dict[str, Any]) -> List[CollectedData]:
        """
        解析API响应数据
        
        Args:
            response_data: API响应数据
            
        Returns:
            List[CollectedData]: 解析后的数据列表
        """
        pass
    
    def _get_default_headers(self) -> Dict[str, str]:
        """
        获取默认HTTP头
        
        Returns:
            Dict[str, str]: HTTP头字典
        """
        headers = {
            'User-Agent': 'IntelligentArticleGenerator/1.0',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }
        
        # 添加API密钥
        if self.config.api_key:
            headers.update(self._get_auth_headers())
        
        return headers
    
    def _get_auth_headers(self) -> Dict[str, str]:
        """
        获取认证头，子类可以重写
        
        Returns:
            Dict[str, str]: 认证头字典
        """
        return {'Authorization': f'Bearer {self.config.api_key}'}
    
    async def _collect_data_impl(
        self, 
        query: str, 
        filters: Optional[Dict[str, Any]] = None
    ) -> List[CollectedData]:
        """
        从外部API收集数据
        
        Args:
            query: 查询字符串
            filters: 过滤条件
            
        Returns:
            List[CollectedData]: 收集到的数据列表
        """
        try:
            # 检查速率限制
            await self.rate_limiter.wait_if_needed()
            
            # 构建API请求
            url = self._get_api_endpoint(query, filters)
            
            self.logger.info(f"请求外部API: {url}")
            
            # 发送请求（会话已绑定代理连接器）
            async with self.session.get(url) as response:
                if response.status == 200:
                    response_data = await response.json()
                    
                    # 解析响应数据
                    collected_data = await self._parse_api_response(response_data)
                    
                    self.logger.info(f"成功收集到 {len(collected_data)} 条数据")
                    return collected_data
                    
                elif response.status == 429:
                    # 速率限制
                    self.data_source.status = self.data_source.status.RATE_LIMITED
                    raise Exception(f"API速率限制: {response.status}")
                    
                else:
                    raise Exception(f"API请求失败: {response.status} - {await response.text()}")
                    
        except Exception as e:
            self.logger.error(f"外部数据收集失败: {str(e)}")
            raise
    
    def _build_query_params(self, query: str, filters: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        构建查询参数
        
        Args:
            query: 查询字符串
            filters: 过滤条件
            
        Returns:
            Dict[str, Any]: 查询参数字典
        """
        params = {'q': query}
        
        if filters:
            params.update(filters)
        
        # 添加通用参数
        params.update({
            'limit': self.config.max_results,
            'sort': 'relevance'
        })
        
        return params
    
    def _parse_datetime(self, date_str: str) -> Optional[datetime]:
        """
        解析日期时间字符串
        
        Args:
            date_str: 日期时间字符串
            
        Returns:
            Optional[datetime]: 解析后的日期时间对象
        """
        if not date_str:
            return None
        
        try:
            # 尝试多种日期格式
            formats = [
                '%Y-%m-%dT%H:%M:%SZ',
                '%Y-%m-%dT%H:%M:%S.%fZ',
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%d',
                '%s'  # Unix时间戳
            ]
            
            for fmt in formats:
                try:
                    if fmt == '%s':
                        # Unix时间戳
                        timestamp = float(date_str)
                        return datetime.fromtimestamp(timestamp, tz=timezone.utc)
                    else:
                        return datetime.strptime(date_str, fmt).replace(tzinfo=timezone.utc)
                except ValueError:
                    continue
            
            self.logger.warning(f"无法解析日期时间: {date_str}")
            return None
            
        except Exception as e:
            self.logger.warning(f"日期时间解析失败: {str(e)}")
            return None
    
    async def shutdown(self) -> None:
        """关闭外部数据源"""
        try:
            if self.session:
                await self.session.close()
                self.session = None
            
            self.logger.info(f"外部数据源 {self.data_source.name} 已关闭")
            
        except Exception as e:
            self.logger.error(f"关闭外部数据源失败: {str(e)}")


class RateLimiter:
    """简单的速率限制器"""
    
    def __init__(self, requests_per_hour: int):
        """
        初始化速率限制器
        
        Args:
            requests_per_hour: 每小时请求数限制
        """
        self.requests_per_hour = requests_per_hour
        self.request_times: List[datetime] = []
        self.lock = asyncio.Lock()
    
    async def wait_if_needed(self) -> None:
        """如果需要，等待以满足速率限制"""
        async with self.lock:
            now = datetime.now(timezone.utc)
            
            # 清理一小时前的请求记录
            cutoff_time = now.replace(hour=now.hour-1) if now.hour > 0 else now.replace(day=now.day-1, hour=23)
            self.request_times = [t for t in self.request_times if t > cutoff_time]
            
            # 检查是否超过限制
            if len(self.request_times) >= self.requests_per_hour:
                # 计算需要等待的时间
                oldest_request = min(self.request_times)
                wait_until = oldest_request.replace(hour=oldest_request.hour+1)
                wait_seconds = (wait_until - now).total_seconds()
                
                if wait_seconds > 0:
                    logger.info(f"速率限制，等待 {wait_seconds:.2f} 秒")
                    await asyncio.sleep(wait_seconds)
            
            # 记录当前请求时间
            self.request_times.append(now)
