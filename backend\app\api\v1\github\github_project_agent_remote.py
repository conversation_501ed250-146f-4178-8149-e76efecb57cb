#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
# <AUTHOR> wangzc
# @Date    : 2025/7/15 13:18
# @File    : github_project_agent_remote.py
# @Description: 
"""
from datetime import datetime

import structlog
import asyncio
import aiohttp
from typing import Dict, Any, List, Optional, Tuple
from app.api.base import BaseHandler
from app.core.config import settings
from app.models.github.github_project import GitHubProjectModel
from app.schemas.github.github_project import GitHubProjectCreate, GitHubProject
from app.utils.flow_log_utils import record_user_flow_log, FlowLogType, record_system_message
from app.utils.status_enum import ProjectStatusEnum
from sqlalchemy import select
from app.services.github.github_project import GitHubProjectService
from dependency_injector.wiring import inject, Provide
from app.core.di.containers import Container

logger = structlog.get_logger(__name__)


# 1.发布机发布命令后仍按原逻辑执行下载-分析命令 但执行到分析步骤时，await由调用ai获得分析结果改为获取分析机心跳以及分析讯息，以判断项目是否分析完成。
# 2.分析机传递结果给发布机时，本质是对发布机进行了一次项目卡片的增删改查
# 3.取消项目时 调用分析机取消的接口。  可能要专门写一个接口

class AnalyzerCalledHandler(BaseHandler):
    """接收命令接口 - 发布机在github_generate_readme 中调用此请求"""

    @inject
    def initialize(
            self,
            github_project_service: GitHubProjectService = Provide[Container.github_project_service]
    ):
        """初始化处理器

        Args:
            github_project_service: GitHub项目服务
        """
        self.github_project_service = github_project_service
        super().initialize()

    async def post(self) -> None:
        try:
            if not settings.distributed.is_analyzer:
                self.write_error(500, error_message="只有分析机可以被调用此接口")
                return

            # 验证请求来源
            # if not self.remote_server.validate_request(self.request.headers):
            #     self.write_error(401, error_message="无效的API密钥")
            #     return

            # 获取请求数据,验证请求信息
            request_data = self.json_body
            publisher_project_id = request_data.get("publisher_project_id")
            if not publisher_project_id:
                self.write_error(500, error_message="缺少publisher_project_id参数")
            projects_data = self.json_body.get("projects", [])
            if not projects_data:
                self.write_error(500, error_message="projects 列表不能为空")
                return
            # 先处理中台分析机里有的项目，已经有的项目就不用
            for project_data in projects_data:
                repository_url = project_data.get("repository_url")
                if not repository_url:
                    continue

                # 检查项目是否已存在
                existing_project = await self.github_project_service.get_project_by_url(repository_url)

                if existing_project and existing_project.project_phase in [
                    ProjectStatusEnum.GenerateSuccess.value,
                    ProjectStatusEnum.SUCCESS.value
                ]:
                    # 项目已存在且已分析完成，直接调用发布机接口
                    logger.info(f"项目 {repository_url} 已存在且已分析完成，直接发送结果给发布机")

                    # 获取项目信息和卡片数据
                    project_info, cards_data = await self._get_project_analysis_data(existing_project.id)

                    # 调用发布机接口
                    success = await self._send_result_to_publisher(
                        analyzer_project_id=existing_project.id,
                        project_info=project_info,
                        cards_data=cards_data
                    )

                    if success:
                        logger.info(f"项目 {repository_url} 分析结果已成功发送给发布机")
                    else:
                        logger.error(f"项目 {repository_url} 分析结果发送给发布机失败")

                    # 继续处理下一个项目
                    continue

            # 再处理分析机里没有的项目
            projects = [GitHubProjectCreate(**project) for project in projects_data]
            # 将发布机的publisher_project_id作为分析机的created_by
            result = await self.github_project_service.download_projects(projects, publisher_project_id)

            processed_result = []
            for item in result:
                processed_item = {
                    "success": item["success"],
                    "error": item["error"]
                }
                # 如果 project 存在且不是 None，转换为字典
                if item["project"] is not None:
                    processed_item["project"] = item["project"].dict()
                else:
                    processed_item["project"] = None

                processed_result.append(processed_item)

            # 返回结果
            self.success_response({
                "github_projects": processed_result,
            })

        except Exception as e:
            logger.error(f"发布任务失败: {str(e)}")
            self.write_error(500, error_message=str(e))

    async def _get_project_analysis_data(self, project_id: str) -> Tuple[Dict[str, Any], List[Dict]]:
        """
        获取项目的分析数据（项目信息和卡片数据）

        Args:
            project_id: 项目ID

        Returns:
            Tuple[Dict[str, Any], List[Dict]]: 项目信息和卡片数据
        """
        try:
            async with self.github_project_service.async_session() as session:
                # 获取项目信息
                project = await self.github_project_service.get_project(project_id)
                if not project:
                    return {}, []

                # 构建项目信息
                project_info = {
                    "slogan": project.slogan,
                    "description": project.description,
                    "feature_tags": project.feature_tags,
                    "phone_shared_data": project.phone_shared_data,
                    "architecture": project.architecture,
                    "dependency": project.dependency
                }

                # 获取项目卡片数据
                cards = await self.github_project_service.get_project_cards(project_id=project_id)
                cards_data = []
                for card in cards:
                    cards_data.append({
                        "title": card.title,
                        "content": card.content,
                        "card_type": card.card_type,
                        "sort_order": card.sort_order,
                        "is_active": card.is_active
                    })

                return project_info, cards_data

        except Exception as e:
            logger.error(f"获取项目分析数据失败: {str(e)}")
            return {}, []

    async def _send_result_to_publisher(self, analyzer_project_id: str,
                                        project_info: Dict[str, Any],
                                        cards_data: List[Dict]) -> bool:
        """
        发送分析结果给发布机

        Args:
            analyzer_project_id: 分析机项目ID
            project_info: 项目信息
            cards_data: 卡片数据

        Returns:
            bool: 发送是否成功
        """
        try:
            # 检查发布机地址是否配置
            if not settings.distributed.PUBLISHER_ENDPOINT:
                logger.error("发布机地址未配置")
                return False

            logger.info(f"开始向发布机发送项目 {analyzer_project_id} 的分析结果")

            # 构建请求数据
            request_data = {
                "analyzer_project_id": analyzer_project_id,
                "success": True,  # 已分析完成的项目肯定是成功的
                "project_info": project_info,
                "cards_data": cards_data
            }

            # 发送POST请求到发布机
            async with aiohttp.ClientSession() as session:
                url = f"{settings.distributed.PUBLISHER_ENDPOINT}/api/v1/remote/agent/publisher-called"

                async with session.post(url, json=request_data) as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.info(f"分析结果发送成功: {result}")
                        return True
                    else:
                        error_text = await response.text()
                        logger.error(f"分析结果发送失败，状态码: {response.status}, 错误: {error_text}")
                        return False

        except Exception as e:
            logger.error(f"发送分析结果时发生错误: {str(e)}")
            return False


class PublisherCalledHandler(BaseHandler):
    """完成分析接口 - 分析机在github_generate_readme 中分析完成后调用此请求以更新卡片"""

    @inject
    def initialize(
            self,
            github_project_service: GitHubProjectService = Provide[Container.github_project_service]
    ):
        """初始化处理器

        Args:
            github_project_service: GitHub项目服务
        """
        self.github_project_service = github_project_service
        super().initialize()

    async def post(self) -> None:
        try:
            if not settings.distributed.is_publisher:
                self.write_error(500, error_message="只有发布机可以被调用此接口")
                return

            # 获取请求数据
            request_data = self.json_body
            if not request_data:
                self.write_error(500, error_message="请求数据不能为空")
                return

            analyzer_project_id = request_data.get("analyzer_project_id")
            success = request_data.get("success", False)
            project_info = request_data.get("project_info", {})
            cards_data = request_data.get("cards_data", [])

            if not analyzer_project_id:
                self.write_error(500, error_message="缺少analyzer_project_id参数")
                return

            logger.info(f"接收到分析机 {analyzer_project_id} 的分析结果，成功状态: {success}")

            if success:
                # 分析成功，更新项目信息和卡片
                await self._handle_successful_analysis(analyzer_project_id, project_info, cards_data)
            else:
                # 分析失败，更新项目状态
                await self._handle_failed_analysis(analyzer_project_id)

            self.success_response({
                "message": "分析结果处理成功",
                "project_id": analyzer_project_id,
                "success": success
            })

        except Exception as e:
            logger.error(f"处理分析结果失败: {str(e)}")
            self.write_error(500, error_message=str(e))

    async def _handle_successful_analysis(self, analyzer_project_id: str,
                                          project_info: Dict[str, Any],
                                          cards_data: List[Dict]) -> None:
        """处理成功的分析结果"""
        try:
            # 1. 更新项目信息
            existing_project = await self.github_project_service.get_project(analyzer_project_id)
            if not existing_project:
                raise Exception(f"项目 {analyzer_project_id} 不存在")

            # 1. 更新项目信息
            # 250827 仍要人工审核   "status": ProjectStatusEnum.FALSE.value
            project_update_data = {
                "id": analyzer_project_id,
                "repository_url": existing_project.repository_url,  # 添加必需字段
                "created_at": existing_project.created_at,  # 添加必需字段
                "updated_at": datetime.utcnow(),  # 更新为当前时间
                "description_recommend": project_info.get("slogan"),
                "description_project": project_info.get("description"),
                "tags": project_info.get("feature_tags"),
                "shared_data": project_info.get("phone_shared_data"),
                "architecture_mermaid": project_info.get("architecture"),
                "dependency_mermaid": project_info.get("dependency"),
                 # "status": ProjectStatusEnum.TRUE.value,
                "status": ProjectStatusEnum.FALSE.value,
                "project_phase": ProjectStatusEnum.GenerateSuccess.value
            }

            # 更新项目
            updated_project = await self.github_project_service.update_project(
                analyzer_project_id,
                project_update_data  # 直接传入字典，而不是 GitHubProject 对象
            )

            if not updated_project:
                raise Exception(f"更新项目 {analyzer_project_id} 失败")

            # 2. 批量更新卡片
            if cards_data:
                # 删除现有卡片并创建新卡片
                created_cards = await self.github_project_service.batch_update_cards(
                    analyzer_project_id,
                    cards_data
                )
                logger.info(f"项目 {analyzer_project_id} 成功创建 {len(created_cards)} 个卡片")
            else:
                logger.warning(f"项目 {analyzer_project_id} 没有卡片数据")

            # 3. 记录流程日志
            await self._record_analysis_logs(analyzer_project_id, True)

            logger.info(f"项目 {analyzer_project_id} 分析结果处理成功")

        except Exception as e:
            logger.error(f"处理成功分析结果时发生错误: {str(e)}")
            raise

    async def _handle_failed_analysis(self, analyzer_project_id: str) -> None:
        """处理失败的分析结果"""
        try:
            existing_project = await self.github_project_service.get_project(analyzer_project_id)
            if not existing_project:
                raise Exception(f"项目 {analyzer_project_id} 不存在")

            # 更新项目状态为失败
            project_update_data = {
                "id": analyzer_project_id,
                "repository_url": existing_project.repository_url,  # 添加必需字段
                "created_at": existing_project.created_at,  # 添加必需字段
                "updated_at": datetime.utcnow(),  # 更新为当前时间
                "status": ProjectStatusEnum.DRAFT.value,
                "project_phase": ProjectStatusEnum.GenerateFail.value
            }

            updated_project = await self.github_project_service.update_project(
                analyzer_project_id,
                GitHubProject(**project_update_data)
            )

            if not updated_project:
                raise Exception(f"更新项目 {analyzer_project_id} 失败")

            # 记录流程日志
            await self._record_analysis_logs(analyzer_project_id, False)

            logger.info(f"项目 {analyzer_project_id} 分析失败状态已更新")

        except Exception as e:
            logger.error(f"处理失败分析结果时发生错误: {str(e)}")
            raise

    async def _record_analysis_logs(self, project_id: str, success: bool) -> None:
        """记录分析流程日志"""
        try:
            async with self.github_project_service.async_session() as session:
                # 获取项目信息
                from sqlalchemy import select
                from app.models.github.github_project import GitHubProjectModel

                stmt = select(GitHubProjectModel).where(GitHubProjectModel.id == project_id)
                result = await session.execute(stmt)
                project = result.scalar_one_or_none()

                if project:
                    if success:
                        await record_user_flow_log(
                            session=session,
                            log_type=FlowLogType.PROJECT_GENERATE,
                            project_id=project.id,
                            project_name=project.name,
                            content=f"{project.repository_url}",
                            created_by=project.created_by
                        )
                        await record_system_message(
                            session=session,
                            message_type=FlowLogType.PROJECT_GENERATE,
                            message=f"项目{project.name}生成完成",
                            created_by=project.created_by
                        )
                    else:
                        await record_system_message(
                            session=session,
                            message_type=FlowLogType.PROJECT_GENERATE,
                            message=f"项目{project.name}生成失败",
                            created_by=project.created_by
                        )

                    await session.commit()

        except Exception as e:
            logger.error(f"记录分析日志时发生错误: {str(e)}")


class AgentHealthCheckHandler(BaseHandler):

    # 发布机不断请求分析机 获取分析机是否在线 以及当前状态
    # 反向请求不知道该返回什么

    @inject
    def initialize(
            self,
            github_project_service: GitHubProjectService = Provide[Container.github_project_service]
    ):
        """初始化处理器

        Args:
            github_project_service: GitHub项目服务
        """
        self.github_project_service = github_project_service
        super().initialize()

    async def get(self) -> None:
        """获取分析机状态和项目分析进度"""
        try:
            if settings.distributed.is_analyzer:
                # 分析机返回自己的状态
                await self._handle_analyzer_heartbeat()
            elif settings.distributed.is_publisher:
                # 发布机请求分析机状态
                # await self._handle_publisher_heartbeat()
                pass
            else:
                self.write_error(500, error_message="无效的部署模式")

        except Exception as e:
            logger.error(f"心跳检查失败: {str(e)}")
            self.write_error(500, error_message=str(e))

    async def _handle_analyzer_heartbeat(self) -> None:
        """处理分析机心跳响应"""
        try:
            # 获取当前分析中的项目状态
            from app.services.github.github_readme_generater import GitHubReadmeGenerate

            # 获取分析队列状态
            queue_status = await GitHubReadmeGenerate.get_generate_queue_status()

            # 获取当前分析中的项目列表
            analyzing_projects = await GitHubReadmeGenerate.get_generate_queue()

            # 构建心跳响应数据
            heartbeat_data = {
                "status": "healthy",
                "timestamp": asyncio.get_event_loop().time(),
                "analyzer_status": {
                    "is_online": True,
                    "queue_status": queue_status,
                    "analyzing_projects": analyzing_projects,
                    "max_concurrent_tasks": GitHubReadmeGenerate._max_concurrent_tasks,
                    "active_tasks": GitHubReadmeGenerate._active_tasks
                }
            }

            self.success_response(heartbeat_data)

        except Exception as e:
            logger.error(f"处理分析机心跳时发生错误: {str(e)}")
            # 返回错误状态
            error_data = {
                "status": "error",
                "timestamp": asyncio.get_event_loop().time(),
                "error": str(e),
                "analyzer_status": {
                    "is_online": False,
                    "queue_status": {},
                    "analyzing_projects": [],
                    "max_concurrent_tasks": 0,
                    "active_tasks": 0
                }
            }
            self.success_response(error_data)

    async def _handle_publisher_heartbeat(self) -> None:
        """处理发布机心跳请求"""
        try:
            # 发布机请求分析机状态
            analyzer_endpoint = settings.distributed.AI_ANALYZER_ENDPOINT
            if not analyzer_endpoint:
                self.write_error(500, error_message="分析机地址未配置")
                return

            # 发送心跳请求到分析机
            async with aiohttp.ClientSession() as session:
                url = f"{analyzer_endpoint}/api/v1/remote/agent/health-check"

                try:
                    async with session.get(url, timeout=10) as response:
                        if response.status == 200:
                            heartbeat_data = await response.json()
                            self.success_response(heartbeat_data)
                        else:
                            # 分析机不可用
                            error_data = {
                                "status": "error",
                                "timestamp": asyncio.get_event_loop().time(),
                                "error": f"分析机响应错误，状态码: {response.status}",
                                "analyzer_status": {
                                    "is_online": False,
                                    "queue_status": {},
                                    "analyzing_projects": [],
                                    "max_concurrent_tasks": 0,
                                    "active_tasks": 0
                                }
                            }
                            self.success_response(error_data)

                except asyncio.TimeoutError:
                    # 分析机超时
                    error_data = {
                        "status": "error",
                        "timestamp": asyncio.get_event_loop().time(),
                        "error": "分析机响应超时",
                        "analyzer_status": {
                            "is_online": False,
                            "queue_status": {},
                            "analyzing_projects": [],
                            "max_concurrent_tasks": 0,
                            "active_tasks": 0
                        }
                    }
                    self.success_response(error_data)

                except Exception as e:
                    # 其他错误
                    error_data = {
                        "status": "error",
                        "timestamp": asyncio.get_event_loop().time(),
                        "error": f"连接分析机失败: {str(e)}",
                        "analyzer_status": {
                            "is_online": False,
                            "queue_status": {},
                            "analyzing_projects": [],
                            "max_concurrent_tasks": 0,
                            "active_tasks": 0
                        }
                    }
                    self.success_response(error_data)

        except Exception as e:
            logger.error(f"处理发布机心跳时发生错误: {str(e)}")
            self.write_error(500, error_message=str(e))




class AnalyzerCancelHandler(BaseHandler):
    """分析机取消接口 - 发布机调用此接口来取消分析机上的分析任务"""

    @inject
    def initialize(
            self,
            github_project_service: GitHubProjectService = Provide[Container.github_project_service]
    ):
        """初始化处理器

        Args:
            github_project_service: GitHub项目服务
        """
        self.github_project_service = github_project_service
        super().initialize()

    async def post(self) -> None:
        """处理分析机取消请求"""
        try:
            # 检查是否为分析机模式
            if not settings.distributed.is_analyzer:
                self.write_error(500, error_message="只有分析机可以被调用此接口")
                return

            # 获取请求数据
            request_data = self.json_body
            if not request_data:
                self.write_error(500, error_message="请求数据不能为空")
                return

            publisher_project_id = request_data.get("publisher_project_id")
            if not publisher_project_id:
                self.write_error(500, error_message="缺少publisher_project_id参数")
                return

            logger.info(f"接收到发布机取消请求，项目ID: {publisher_project_id}")

            # 查找对应的分析机项目ID
            # 注意：发布机的project_id是分析机的created_by
            async with self.github_project_service.async_session() as session:
                # 查找以publisher_project_id为created_by的项目
                stmt = select(GitHubProjectModel).where(
                    GitHubProjectModel.created_by == publisher_project_id
                )
                result = await session.execute(stmt)
                analyzer_project = result.scalar_one_or_none()

                if not analyzer_project:
                    logger.warning(f"未找到对应的分析机项目，publisher_project_id: {publisher_project_id}")
                    self.success_response({
                        "success": False,
                        "message": "未找到对应的分析机项目",
                        "publisher_project_id": publisher_project_id
                    })
                    return

                analyzer_project_id = str(analyzer_project.id)
                logger.info(f"找到对应的分析机项目ID: {analyzer_project_id}")

                # 调用分析机的取消方法
                from app.services.github.github_readme_generater import GitHubReadmeGenerate
                cancel_success = await GitHubReadmeGenerate.cancel_project_generation(analyzer_project_id)

                if cancel_success:
                    # 更新项目状态为取消
                    analyzer_project.project_phase = ProjectStatusEnum.CANCELLED.value
                    await session.commit()

                    # 记录取消日志
                    await record_user_flow_log(
                        session=session,
                        log_type=FlowLogType.PROJECT_CANCEL,
                        project_id=analyzer_project.id,
                        project_name=analyzer_project.name,
                        content=f"分析机取消项目: {analyzer_project.repository_url}",
                        created_by=analyzer_project.created_by
                    )

                    await record_system_message(
                        session=session,
                        message_type=FlowLogType.PROJECT_CANCEL,
                        message=f"分析机项目{analyzer_project.name}已取消",
                        created_by=analyzer_project.created_by
                    )

                    logger.info(f"分析机项目 {analyzer_project_id} 取消成功")
                    self.success_response({
                        "success": True,
                        "message": "分析机项目取消成功",
                        "publisher_project_id": publisher_project_id,
                        "analyzer_project_id": analyzer_project_id
                    })
                else:
                    logger.error(f"分析机项目 {analyzer_project_id} 取消失败")
                    self.success_response({
                        "success": False,
                        "message": "分析机项目取消失败",
                        "publisher_project_id": publisher_project_id,
                        "analyzer_project_id": analyzer_project_id
                    })

        except Exception as e:
            logger.error(f"处理分析机取消请求失败: {str(e)}")
            self.write_error(500, error_message=str(e))

# ... existing code ...