#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据源管理器

统一管理所有内部和外部数据源，提供数据收集和聚合功能
"""

import asyncio
import logging
from typing import Any, Dict, List, Optional, Tuple
from datetime import datetime, timezone

from app.core.di.providers import SessionProvider, AsyncSessionProvider
from .base_data_source import BaseDataSource
from .internal_sources import ReadmeSource, AnalysisSource, ProjectSource, UniversalSearchInternalSource
from .external_sources import (
    StackOverflowSource, RedditSource, HackerNewsSource, DevToSource,
    ProductHuntSource, GitHubSource, MediumSource, ArxivSource,
    YouTubeSource, TechBlogAggregatorSource, TechNewsSource,
    # 国内数据源
    JuejinSource, CSDNSource, ZhihuSource, SegmentFaultSource, CNBlogsSource,
    OSChinaSource, WeiboSource, BilibiliSource, DouyinSource, XiaohongshuSource
)
from .internal_sources.news_source import NewsSource
from ..models.data_source_models import (
    DataSourceType,
    ZHIHU_COOKIES,
    DataSourceConfig,
    CollectedData,
    DataSourceMetrics,
    DataQuality
)
from ...workflow.models import AnalysisState

logger = logging.getLogger(__name__)


class DataSourceManager:
    """数据源管理器"""

    def __init__(self, session: SessionProvider,async_session: AsyncSessionProvider):
        """
        初始化数据源管理器
        """
        self.session = session
        self.async_session = async_session
        self.data_sources: Dict[DataSourceType, BaseDataSource] = {}
        self.initialized = False
        self.analysis_state: Optional[AnalysisState] = None
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    async def initialize(self, configs: Optional[Dict[DataSourceType, DataSourceConfig]] = None) -> bool:
        """
        初始化所有数据源
        
        Args:
            configs: 数据源配置字典
            
        Returns:
            bool: 初始化是否成功
        """
        try:
            self.logger.info("开始初始化数据源管理器")
            
            # 使用默认配置或提供的配置
            if configs is None:
                configs = self._get_default_configs()
            
            # 初始化各个数据源
            initialization_tasks = []
            
            for source_type, config in configs.items():
                if config.enabled:
                    try:
                        data_source = self._create_data_source(source_type, config)
                        if data_source:
                            self.data_sources[source_type] = data_source
                            initialization_tasks.append(data_source.initialize())
                    except Exception as e:
                        self.logger.error(f"创建数据源 {source_type} 失败: {str(e)}")
            
            # 并发初始化所有数据源
            if initialization_tasks:
                results = await asyncio.gather(*initialization_tasks, return_exceptions=True)
                
                # 检查初始化结果
                successful_count = 0
                for i, result in enumerate(results):
                    source_type = list(self.data_sources.keys())[i]
                    if isinstance(result, Exception):
                        self.logger.error(f"数据源 {source_type} 初始化失败: {str(result)}")
                    elif result:
                        successful_count += 1
                        self.logger.info(f"数据源 {source_type} 初始化成功")
                    else:
                        self.logger.warning(f"数据源 {source_type} 初始化失败")
                
                self.logger.info(f"成功初始化 {successful_count}/{len(initialization_tasks)} 个数据源")
                self.initialized = successful_count > 0
            else:
                self.logger.warning("没有启用的数据源")
                self.initialized = False

            return self.initialized

        except Exception as e:
            self.logger.error(f"数据源管理器初始化失败: {str(e)}")
            return False

    async def _setup_internal_data_sources(self) -> None:
        """
        设置内部数据源的分析数据
        """
        try:
            # 设置分析数据源
            analysis_source = self.data_sources.get(DataSourceType.ANALYSIS)
            if analysis_source and isinstance(analysis_source, AnalysisSource):
                analysis_source.set_analysis_data(
                    module_merger_analysis=self.analysis_state.module_merger_analyses if self.analysis_state else None,
                    architecture_analysis=self.analysis_state.architecture_analysis if self.analysis_state else None,
                    dependency_analysis=self.analysis_state.dependency_analysis if self.analysis_state else None,
                    structure_analysis=self.analysis_state.structure_analysis if self.analysis_state else None,
                    analysis_state=self.analysis_state
                )
                self.logger.info("已设置分析数据源的分析数据")

            # 设置项目数据源
            project_source = self.data_sources.get(DataSourceType.PROJECT)
            if project_source and isinstance(project_source, ProjectSource):
                project_source.set_analysis_data(
                    analysis_state=self.analysis_state,
                    architecture_analysis=self.analysis_state.architecture_analysis if self.analysis_state else None,
                    dependency_analysis=self.analysis_state.dependency_analysis if self.analysis_state else None,
                    structure_analysis=self.analysis_state.structure_analysis if self.analysis_state else None,
                    project_structure=self.analysis_state.project_structure if self.analysis_state else None
                )
                self.logger.info("已设置项目数据源的分析数据")

            # 设置README数据源
            readme_source = self.data_sources.get(DataSourceType.README)
            if readme_source and isinstance(readme_source, ReadmeSource):
                readme_source.set_readme_data(
                    readme_document=self.analysis_state.readme if self.analysis_state else None,
                    analysis_state=self.analysis_state
                )
                self.logger.info("已设置README数据源的分析数据")

        except Exception as e:
            self.logger.error(f"设置内部数据源分析数据失败: {str(e)}")

    async def set_analysis_data(self, analysis_state: AnalysisState) -> None:
        """
        设置分析数据并配置内部数据源

        Args:
            analysis_state: 分析状态对象
        """
        self.analysis_state = analysis_state
        if self.initialized:
            await self._setup_internal_data_sources()
            self.logger.info("已设置分析数据并配置内部数据源")

    def _get_default_configs(self) -> Dict[DataSourceType, DataSourceConfig]:
        """
        获取默认数据源配置

        Returns:
            Dict[DataSourceType, DataSourceConfig]: 默认配置字典
        """
        configs = {
            # 内部数据源
            # DataSourceType.README: DataSourceConfig(
            #     source_type=DataSourceType.README,
            #     enabled=True,
            #     priority=10,
            #     max_results=20
            # ),
            # DataSourceType.ANALYSIS: DataSourceConfig(
            #     source_type=DataSourceType.ANALYSIS,
            #     enabled=True,
            #     priority=9,
            #     max_results=15
            # ),
            # DataSourceType.PROJECT: DataSourceConfig(
            #     source_type=DataSourceType.PROJECT,
            #     enabled=True,
            #     priority=8,
            #     max_results=10
            # ),
            DataSourceType.INTERNAL_DB: DataSourceConfig(
                source_type=DataSourceType.INTERNAL_DB,
                enabled=True,
                priority=9,
                max_results=20,
            ),

            DataSourceType.NEWS: DataSourceConfig(
                source_type=DataSourceType.NEWS,
                enabled=True,
                priority=8,
                max_results=10
            ),

            # # 外部数据源
            DataSourceType.STACKOVERFLOW: DataSourceConfig(
                source_type=DataSourceType.STACKOVERFLOW,
                enabled=True,
                priority=7,
                rate_limit=300,
                max_results=30,
                api_url="https://api.stackexchange.com/2.3"
            ),
            DataSourceType.REDDIT: DataSourceConfig(
                source_type=DataSourceType.REDDIT,
                enabled=True,  # 默认禁用，需要API密钥
                priority=5,
                rate_limit=60,
                max_results=20,
                api_url="https://www.reddit.com",
                api_key="f8hC_EYp_Gn_U2Sx-HCzdA",
                api_secret="3BuXEru15ApUe-0kCTF4tT4gva-n9A"
            ),
            DataSourceType.HACKERNEWS: DataSourceConfig(
                source_type=DataSourceType.HACKERNEWS,
                enabled=True,
                priority=6,
                rate_limit=100,
                max_results=25,
                api_url="https://hacker-news.firebaseio.com/v0"
            ),
            DataSourceType.DEVTO: DataSourceConfig(
                source_type=DataSourceType.DEVTO,
                enabled=True,
                priority=6,
                rate_limit=1000,
                max_results=25,
                api_url="https://dev.to/api"
            ),
            DataSourceType.GITHUB: DataSourceConfig(
                source_type=DataSourceType.GITHUB,
                enabled=True,
                priority=7,
                rate_limit=5000,
                max_results=30,
                api_url="https://api.github.com"
            ),
                # DataSourceType.MEDIUM: DataSourceConfig(
                #     source_type=DataSourceType.MEDIUM,
                #     enabled=True,
                #     priority=6,
                #     rate_limit=200,
                #     max_results=25,
                #     api_url="https://medium.com"
                # ),
            DataSourceType.ARXIV: DataSourceConfig(
                source_type=DataSourceType.ARXIV,
                enabled=True,
                priority=8,
                rate_limit=100,
                max_results=20,
                api_url="http://export.arxiv.org/api"
            ),
                # DataSourceType.YOUTUBE: DataSourceConfig(
                #     source_type=DataSourceType.YOUTUBE,
                #     enabled=False,  # 需要API密钥
                #     priority=7,
                #     rate_limit=10000,
                #     max_results=25,
                #     api_url="https://www.googleapis.com/youtube/v3"
                # ),
                # DataSourceType.TECHBLOG: DataSourceConfig(
                #     source_type=DataSourceType.TECHBLOG,
                #     enabled=True,
                #     priority=7,
                #     rate_limit=500,
                #     max_results=30,
                #     api_url="https://feeds.feedburner.com"
                # ),
                # DataSourceType.TECHNEWS: DataSourceConfig(
                #     source_type=DataSourceType.TECHNEWS,
                #     enabled=False,  # 需要API密钥
                #     priority=6,
                #     rate_limit=1000,
                #     max_results=30,
                #     api_url="https://newsapi.org/v2"
                # ),
            DataSourceType.PRODUCTHUNT: DataSourceConfig(
                source_type=DataSourceType.PRODUCTHUNT,
                enabled=True,  # 默认禁用，需要API密钥
                priority=4,
                rate_limit=100,
                max_results=15,
                api_url="https://api.producthunt.com/v2/api/graphql",
                api_key="0MIF-wHr86GXTKhoshhROAX_ref7DfDvlf3A76qhFQ0",
                api_secret="yM3_3ej26dIJ9Vlv8Mzmk7N6yuAmtaRODvsXjgtYYJ4"
            ),

            # 国内数据源
            DataSourceType.JUEJIN: DataSourceConfig(
                source_type=DataSourceType.JUEJIN,
                enabled=True,
                priority=7,
                rate_limit=500,
                max_results=20,
                api_url="https://api.juejin.cn/content_api/v1"
            ),
            DataSourceType.CSDN: DataSourceConfig(
                source_type=DataSourceType.CSDN,
                enabled=True,
                priority=6,
                rate_limit=300,
                max_results=20,
                api_url="https://so.csdn.net/api/v3"
            ),
                # DataSourceType.ZHIHU: DataSourceConfig(
                #     source_type=DataSourceType.ZHIHU,
                #     enabled=True,
                #     priority=7,
                #     rate_limit=200,
                #     max_results=15,
                #     api_key=ZHIHU_COOKIES,
                #     api_url="https://www.zhihu.com/api/v4"
                # ),
                # DataSourceType.SEGMENTFAULT: DataSourceConfig(
                #     source_type=DataSourceType.SEGMENTFAULT,
                #     enabled=True,
                #     priority=6,
                #     rate_limit=400,
                #     max_results=20,
                #     api_url="https://segmentfault.com/api"
                # ),
                # DataSourceType.CNBLOGS: DataSourceConfig(
                #     source_type=DataSourceType.CNBLOGS,
                #     enabled=True,
                #     priority=6,
                #     rate_limit=300,
                #     max_results=20,
                #     api_url="https://zzk.cnblogs.com"
                # ),
            DataSourceType.OSCHINA: DataSourceConfig(
                source_type=DataSourceType.OSCHINA,
                enabled=True,
                priority=6,
                rate_limit=400,
                max_results=20,
                api_key="mBxHI7oZRTVAjc8EsukF",
                api_secret="7HBOXDJr2TBiirNukkAN3CZELR40YdTL",
                api_url="https://www.oschina.net",
                supports_hot_rank=True
            ),
            # DataSourceType.WEIBO: DataSourceConfig(
            #     source_type=DataSourceType.WEIBO,
            #     enabled=False,  # 默认禁用，需要API密钥
            #     priority=5,
            #     rate_limit=150,
            #     max_results=15,
            #     api_url="https://api.weibo.com/2"
            # ),
            # DataSourceType.BILIBILI: DataSourceConfig(
            #     source_type=DataSourceType.BILIBILI,
            #     enabled=True,
            #     priority=6,
            #     rate_limit=300,
            #     max_results=20,
            #     api_url="https://api.bilibili.com"
            # ),
            # DataSourceType.DOUYIN: DataSourceConfig(
            #     source_type=DataSourceType.DOUYIN,
            #     enabled=False,  # 默认禁用，需要API密钥
            #     priority=5,
            #     rate_limit=100,
            #     max_results=15,
            #     api_url="https://open.douyin.com/api"
            # ),
            # DataSourceType.XIAOHONGSHU: DataSourceConfig(
            #     source_type=DataSourceType.XIAOHONGSHU,
            #     enabled=False,  # 默认禁用，需要API密钥
            #     priority=5,
            #     rate_limit=120,
            #     max_results=15,
            #     api_url="https://edith.xiaohongshu.com/api"
            # )
        }

        return configs
    
    def _create_data_source(self, source_type: DataSourceType, config: DataSourceConfig) -> Optional[BaseDataSource]:
        """
        创建数据源实例
        
        Args:
            source_type: 数据源类型
            config: 数据源配置
            
        Returns:
            Optional[BaseDataSource]: 数据源实例
        """
        try:
            # 内部数据源
            if source_type == DataSourceType.README:
                return ReadmeSource(config)
            elif source_type == DataSourceType.ANALYSIS:
                return AnalysisSource(config)
            elif source_type == DataSourceType.PROJECT:
                return ProjectSource(config)
            elif source_type == DataSourceType.INTERNAL_DB:
                # 使用通用搜索引擎的内部数据库型数据源
                return UniversalSearchInternalSource(config)
            elif source_type == DataSourceType.NEWS:
                # 使用新闻的 内部数据库型数据源
                return NewsSource(async_session_provider=self.async_session,config=config)
            # 外部数据源
            elif source_type == DataSourceType.STACKOVERFLOW:
                return StackOverflowSource(config)
            elif source_type == DataSourceType.REDDIT:
                return RedditSource(config)
            elif source_type == DataSourceType.HACKERNEWS:
                return HackerNewsSource(config)
            elif source_type == DataSourceType.DEVTO:
                return DevToSource(config)
            elif source_type == DataSourceType.PRODUCTHUNT:
                return ProductHuntSource(config)
            elif source_type == DataSourceType.GITHUB:
                return GitHubSource(config)
            elif source_type == DataSourceType.MEDIUM:
                return MediumSource(config)
            elif source_type == DataSourceType.ARXIV:
                return ArxivSource(config)
            elif source_type == DataSourceType.YOUTUBE:
                return YouTubeSource(config)
            elif source_type == DataSourceType.TECHBLOG:
                return TechBlogAggregatorSource(config)
            elif source_type == DataSourceType.TECHNEWS:
                return TechNewsSource(config)

            # 国内数据源
            elif source_type == DataSourceType.JUEJIN:
                return JuejinSource(config)
            elif source_type == DataSourceType.CSDN:
                return CSDNSource(config)
            elif source_type == DataSourceType.ZHIHU:
                return ZhihuSource(config)
            elif source_type == DataSourceType.SEGMENTFAULT:
                return SegmentFaultSource(config)
            elif source_type == DataSourceType.CNBLOGS:
                return CNBlogsSource(config)
            elif source_type == DataSourceType.OSCHINA:
                return OSChinaSource(config)
            elif source_type == DataSourceType.WEIBO:
                return WeiboSource(config)
            elif source_type == DataSourceType.BILIBILI:
                return BilibiliSource(config)
            elif source_type == DataSourceType.DOUYIN:
                return DouyinSource(config)
            elif source_type == DataSourceType.XIAOHONGSHU:
                return XiaohongshuSource(config)
            else:
                # why return None here no exception throw？ - 250827
                self.logger.warning(f"未知的数据源类型: {source_type}")
                return None
                
        except Exception as e:

            self.logger.error(f"创建数据源 {source_type} 失败: {str(e)}")
            return None
    
    async def collect_all_data(
        self, 
        query: str, 
        filters: Optional[Dict[str, Any]] = None,
        source_types: Optional[List[DataSourceType]] = None
    ) -> Tuple[List[CollectedData], Dict[DataSourceType, DataSourceMetrics]]:
        """
        从所有启用的数据源收集数据
        
        Args:
            query: 查询字符串
            filters: 过滤条件
            source_types: 指定的数据源类型列表，如果为None则使用所有启用的数据源
            
        Returns:
            Tuple[List[CollectedData], Dict[DataSourceType, DataSourceMetrics]]: 收集的数据和指标
        """
        if not self.initialized:
            raise RuntimeError("数据源管理器未初始化")
        
        # 确定要使用的数据源
        target_sources = source_types or list(self.data_sources.keys())
        active_sources = {
            source_type: source for source_type, source in self.data_sources.items()
            if source_type in target_sources and source.is_healthy()
        }
        
        if not active_sources:
            self.logger.warning("没有可用的数据源")
            return [], {}
        
        self.logger.info(f"开始从 {len(active_sources)} 个数据源收集数据，查询: {query}")
        
        # 并发收集数据
        collection_tasks = []
        for source_type, source in active_sources.items():
            task = asyncio.create_task(
                self._collect_from_source(source, query, filters),
                name=f"collect_{source_type.value}"
            )
            collection_tasks.append((source_type, task))
        
        # 等待所有任务完成
        all_data = []
        all_metrics = {}
        
        for source_type, task in collection_tasks:
            try:
                data, metrics = await task
                all_data.extend(data)
                all_metrics[source_type] = metrics
                self.logger.info(f"从 {source_type.value} 收集到 {len(data)} 条数据")
            except Exception as e:
                self.logger.error(f"从 {source_type.value} 收集数据失败: {str(e)}")
                # 创建错误指标
                all_metrics[source_type] = DataSourceMetrics(
                    source_id=self.data_sources[source_type].data_source.id,
                    collection_time=datetime.now(timezone.utc),
                    data_count=0,
                    average_quality=0.0,
                    processing_time=0.0,
                    errors=[str(e)]
                )
        
        # 数据去重和排序
        deduplicated_data = self._deduplicate_data(all_data)
        sorted_data = self._sort_data_by_quality(deduplicated_data)
        
        self.logger.info(f"总共收集到 {len(sorted_data)} 条去重后的数据")
        return sorted_data, all_metrics
    
    async def _collect_from_source(
        self, 
        source: BaseDataSource, 
        query: str, 
        filters: Optional[Dict[str, Any]]
    ) -> Tuple[List[CollectedData], DataSourceMetrics]:
        """
        从单个数据源收集数据
        
        Args:
            source: 数据源实例
            query: 查询字符串
            filters: 过滤条件
            
        Returns:
            Tuple[List[CollectedData], DataSourceMetrics]: 收集的数据和指标
        """
        try:
            return await source.collect_data(query, filters)
        except Exception as e:
            self.logger.error(f"从数据源收集数据失败: {str(e)}")
            raise
    
    def _deduplicate_data(self, data_list: List[CollectedData]) -> List[CollectedData]:
        """
        数据去重
        
        Args:
            data_list: 数据列表
            
        Returns:
            List[CollectedData]: 去重后的数据列表
        """
        seen_content = set()
        deduplicated = []
        
        for data in data_list:
            # 使用标题和内容的前100个字符作为去重键
            content_key = f"{data.title}_{data.content[:100]}"
            content_hash = hash(content_key)
            
            if content_hash not in seen_content:
                seen_content.add(content_hash)
                deduplicated.append(data)
        
        removed_count = len(data_list) - len(deduplicated)
        if removed_count > 0:
            self.logger.info(f"去重移除了 {removed_count} 条重复数据")
        
        return deduplicated
    
    def _sort_data_by_quality(self, data_list: List[CollectedData]) -> List[CollectedData]:
        """
        按质量评分排序数据
        
        Args:
            data_list: 数据列表
            
        Returns:
            List[CollectedData]: 排序后的数据列表
        """
        def get_quality_score(data: CollectedData) -> float:
            if data.quality:
                return data.quality.overall_score
            return 0.5  # 默认评分
        
        return sorted(data_list, key=get_quality_score, reverse=True)
    
    async def get_hot_items(
        self,
        limit: int = 50,
        source_types: Optional[List[DataSourceType]] = None
    ) -> Dict[DataSourceType, List[CollectedData]]:
        """按数据源类型返回热点数据
        
        从所有已初始化且健康，并且配置了 supports_hot_rank=True 的数据源并发获取热点数据，
        直接以“数据源类型 -> 列表”的形式返回，不进行去重、聚合或排序。
        
        Args:
            limit: 每个数据源返回的最大条数
            source_types: 指定只从这些类型中获取；默认所有支持热点的启用源
        
        Returns:
            Dict[DataSourceType, List[CollectedData]]: 按数据源类型分组的热点数据
        """
        if not self.initialized:
            raise RuntimeError("数据源管理器未初始化")

        # 选择目标数据源：支持热点且健康
        selected: Dict[DataSourceType, BaseDataSource] = {}
        for st, src in self.data_sources.items():
            try:
                if source_types and st not in source_types:
                    continue
                cfg = src.config
                if getattr(cfg, 'supports_hot_rank', False) and src.is_healthy():
                    selected[st] = src
            except Exception:
                continue

        if not selected:
            self.logger.info("没有支持热点的数据源或均不可用")
            return {}

        self.logger.info(f"开始从 {len(selected)} 个数据源获取热点数据")

        # 并发获取热点
        tasks: List[asyncio.Task] = []
        types: List[DataSourceType] = []
        for st, src in selected.items():
            types.append(st)
            tasks.append(asyncio.create_task(src.get_hot_items(limit=limit), name=f"hot_{st.value}"))

        grouped: Dict[DataSourceType, List[CollectedData]] = {}
        completed = await asyncio.gather(*tasks, return_exceptions=True)
        for st, res in zip(types, completed):
            if isinstance(res, Exception):
                self.logger.error(f"获取 {st.value} 热点失败: {res}")
                grouped[st] = []
            else:
                grouped[st] = res or []
                self.logger.info(f"{st.value} 返回热点 {len(grouped[st])} 条")

        return grouped
    
    def get_source_status(self) -> Dict[DataSourceType, Dict[str, Any]]:
        """
        获取所有数据源的状态
        
        Returns:
            Dict[DataSourceType, Dict[str, Any]]: 数据源状态字典
        """
        status = {}
        
        for source_type, source in self.data_sources.items():
            source_data = source.get_status()
            status[source_type] = {
                'name': source_data.name,
                'status': source_data.status.value,
                'is_healthy': source.is_healthy(),
                'total_requests': source_data.total_requests,
                'success_rate': source_data.get_success_rate(),
                'last_success_at': source_data.last_success_at,
                'last_error': source_data.last_error,
                'total_data_collected': source_data.total_data_collected,
                'average_quality_score': source_data.average_quality_score
            }
        
        return status
    
    def set_project_context(self, project_path: str, project_data: Dict[str, Any]) -> None:
        """
        为内部数据源设置项目上下文
        
        Args:
            project_path: 项目路径
            project_data: 项目数据
        """
        internal_source_types = [
            DataSourceType.README,
            DataSourceType.ANALYSIS,
            DataSourceType.PROJECT,
            DataSourceType.INTERNAL_DB,
        ]
        
        for source_type in internal_source_types:
            if source_type in self.data_sources:
                source = self.data_sources[source_type]
                if hasattr(source, 'set_project_context'):
                    source.set_project_context(project_path, project_data)
        
        self.logger.info(f"为内部数据源设置项目上下文: {project_path}")
    
    async def shutdown(self) -> None:
        """关闭所有数据源"""
        try:
            shutdown_tasks = []
            
            for source in self.data_sources.values():
                if hasattr(source, 'shutdown'):
                    shutdown_tasks.append(source.shutdown())
            
            if shutdown_tasks:
                await asyncio.gather(*shutdown_tasks, return_exceptions=True)
            
            self.data_sources.clear()
            self.initialized = False
            self.logger.info("数据源管理器已关闭")
            
        except Exception as e:
            self.logger.error(f"关闭数据源管理器失败: {str(e)}")
