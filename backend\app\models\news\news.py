"""
新闻数据库模型 - 简化版本，只包含文本内容
"""
from datetime import datetime
from sqlalchemy import Column, String, Text, TIMESTAMP

from app.models.model_base import ModelBase


class NewsModel(ModelBase):
    """新闻表 - 简化版本"""
    __tablename__ = "history_news"

    # 基本信息 - 只包含文本内容
    title = Column(String(255), nullable=False, comment='新闻标题')
    content = Column(Text, nullable=False, comment='新闻内容')
    
    # 可选的基本信息
    source = Column(String(100), nullable=True, comment='新闻来源')
    
    # 发布时间
    published_at = Column(TIMESTAMP(timezone=True), nullable=True, comment='发布时间')

    def __repr__(self):
        return f"<News {self.title}>"

