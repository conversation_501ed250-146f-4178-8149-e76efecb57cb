<template>
  <div class="ai-write-preset">
    <el-popover
      placement="top-start"
      title=""
      width="fit-content"
      trigger="hover">
      <template #reference>
        <el-button>
          报告
          <el-icon><ArrowRight /></el-icon>
        </el-button>
      </template>
      <div class="list">
        <div class="list-item" v-for="item in reportList">{{ item.name }}</div>
        <el-icon class="add-icon" size="22px">
          <CirclePlus />
        </el-icon>
      </div>
    </el-popover>
    <el-popover
      placement="top-start"
      title=""
      width="fit-content"
      trigger="hover">
      <template #reference>
        <el-button>
          文章结构
          <el-icon><ArrowRight /></el-icon>
        </el-button>
      </template>
      <div class="list">
        <div class="list-item" v-for="item in structureList">
          {{ item.name }}
        </div>
        <el-icon class="add-icon" size="22px">
          <CirclePlus />
        </el-icon>
      </div>
    </el-popover>
    <el-dialog
      title=""
      v-model="reportDialogVisible"
      width="500px"
      append-to-body
      align-center>
      <el-checkbox-group v-model="checkList">
        <el-checkbox label="主站" value="a" />
        <el-checkbox label="服务号" value="b" />
        <el-checkbox label="公众号" value="c" />
      </el-checkbox-group>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm1">确认</el-button>
          <el-button @click="visible1 = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
let reportList = ref([
  {
    name: "项目报告",
    id: 1,
  },
  { name: "代码报告", id: 2 },
  { name: "产品报告", id: 3 },
]);
let structureList = ref([
  { name: "学术论文", id: 1 },
  { name: "小学生作文", id: 2 },
  { name: "散文", id: 3 },
]);
</script>

<style scoped lang="scss">
.ai-write-preset {
  height: 40px;
  width: 100%;
  display: flex;
  align-items: center;
  padding-top: 4px;
}
.list {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 6px;
  .list-item {
    width: 100%;
    max-width: 300px;
    text-align: center;
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    &.active {
      color: #409eff;
    }
    &:hover {
      background-color: #eee;
    }
  }
  .add-icon {
    cursor: pointer;
    color: #77b6f5;
    &:hover {
      color: #409eff;
    }
  }
}
</style>
